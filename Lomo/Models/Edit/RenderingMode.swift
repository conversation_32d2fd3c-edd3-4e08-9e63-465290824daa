import Foundation

/// 渲染模式枚举 - 区分不同的滤镜算法
enum RenderingMode {
    case lightroom  // Lightroom风格算法 - 用于传统调整和非胶片滤镜
    case vsco      // VSCO风格算法 - 用于胶片滤镜
    
    var displayName: String {
        switch self {
        case .lightroom:
            return "标准调整模式"
        case .vsco:
            return "胶片调整模式"
        }
    }
    
    var shaderFunctionName: String {
        switch self {
        case .lightroom:
            return "lightroom_filter"
        case .vsco:
            return "vsco_filter"
        }
    }
    
    var description: String {
        switch self {
        case .lightroom:
            return "基于Adobe Lightroom算法的专业调色"
        case .vsco:
            return "基于胶片特性的艺术化处理"
        }
    }
}

/// 滤镜类型到渲染模式的映射
extension FilterPresetType {
    var renderingMode: RenderingMode {
        switch self {
        case .film:
            return .vsco  // 胶片滤镜使用VSCO算法
        case .polaroid, .vintage, .fashion, .ins:
            return .lightroom  // 其他滤镜使用Lightroom算法
        }
    }
}
