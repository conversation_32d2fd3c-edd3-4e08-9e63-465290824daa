import SwiftUI

/// 滤镜应用视图 - MVVM-S架构
struct FilterView: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height

    // 视图模型
    @ObservedObject var filterViewModel: FilterViewModel

    init(filterViewModel: FilterViewModel) {
        self.filterViewModel = filterViewModel
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 0) {
                // 宝丽来
                FilterCategorySection(
                    title: "宝丽来",
                    type: .polaroid,
                    filterViewModel: filterViewModel,
                    screenWidth: screenWidth,
                    screenHeight: screenHeight
                )

                // 胶卷
                FilterCategorySection(
                    title: "胶卷",
                    type: .film,
                    filterViewModel: filterViewModel,
                    screenWidth: screenWidth,
                    screenHeight: screenHeight
                )

                // 复古
                FilterCategorySection(
                    title: "复古",
                    type: .vintage,
                    filterViewModel: filterViewModel,
                    screenWidth: screenWidth,
                    screenHeight: screenHeight
                )

                // 时尚
                FilterCategorySection(
                    title: "时尚",
                    type: .fashion,
                    filterViewModel: filterViewModel,
                    screenWidth: screenWidth,
                    screenHeight: screenHeight
                )

                // INS
                FilterCategorySection(
                    title: "INS",
                    type: .ins,
                    filterViewModel: filterViewModel,
                    screenWidth: screenWidth,
                    screenHeight: screenHeight
                )
            }
        }
        .safeAreaInset(edge: .bottom) {
            Color.clear.frame(height: screenHeight * 0.06)
        }
        .frame(maxWidth: .infinity)
    }
}

// MARK: - 滤镜分类区域组件

struct FilterCategorySection: View {
    let title: String
    let type: FilterPresetType
    @ObservedObject var filterViewModel: FilterViewModel
    let screenWidth: CGFloat
    let screenHeight: CGFloat

    var body: some View {
        VStack(alignment: .leading, spacing: screenHeight * 0.01) {
            Text(title)
                .font(.system(size: screenHeight * 0.02, weight: .bold))
                .foregroundColor(.white)

            // 预设选择（水平滚动）
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: screenWidth * 0.02) {
                    ForEach(0..<5) { index in
                        FilterPresetButton(
                            type: type,
                            index: index,
                            filterViewModel: filterViewModel,
                            screenWidth: screenWidth,
                            screenHeight: screenHeight
                        )
                    }
                }
                .padding(.horizontal, screenWidth * 0.02)
            }
        }
        .padding(.horizontal, screenWidth * 0.04)
        .padding(.vertical, screenHeight * 0.01)
    }
}

// MARK: - 滤镜预设按钮组件

struct FilterPresetButton: View {
    let type: FilterPresetType
    let index: Int
    @ObservedObject var filterViewModel: FilterViewModel
    let screenWidth: CGFloat
    let screenHeight: CGFloat

    // 获取预设信息
    private var preset: FilterPreset? {
        filterViewModel.getPresets(for: type).first { $0.index == index }
    }

    // 检查是否选中
    private var isSelected: Bool {
        filterViewModel.isPresetSelected(type: type, index: index)
    }

    var body: some View {
        Button(action: {
            // 检查是否已经选中当前预设
            if filterViewModel.isPresetSelected(type: type, index: index) {
                // 如果已选中，则取消选中，显示原图
                filterViewModel.clearPreset()
                print("🎨 取消选中滤镜，显示原图")
            } else {
                // 如果未选中，则应用预设
                filterViewModel.applyPreset(type: type, index: index)
                print("🎨 应用滤镜预设: \(type.rawValue) - \(index)")
            }
        }) {
            VStack(spacing: screenHeight * 0.005) {
                Image("LH Preview")
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: screenWidth * 0.15)
                    .clipped()
                    .cornerRadius(6)
                    .overlay(alignment: .bottomTrailing) {
                        // 显示选中状态
                        if isSelected {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(UIConstants.dialIndicatorColor)
                                .font(.system(size: screenHeight * 0.015))
                                .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                        }
                    }

                Text(preset?.name ?? "预设\(index + 1)")
                    .font(.system(size: screenHeight * 0.012))
                    .foregroundColor(.white)
                    .lineLimit(1)
            }
        }
    }
}

// MARK: - 预览支持

struct FilterView_Previews: PreviewProvider {
    static var previews: some View {
        FilterView(filterViewModel: FilterViewModel())
            .background(Color.black)
    }
}