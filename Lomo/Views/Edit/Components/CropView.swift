import SwiftUI

/// 裁切控制视图 - MVVM-S架构
struct CropView: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height

    // MVVM-S架构：使用CropViewModel
    @StateObject private var cropViewModel: CropViewModel

    // 初始化方法 - 依赖注入
    init(cropViewModel: CropViewModel) {
        self._cropViewModel = StateObject(wrappedValue: cropViewModel)
    }

    // 便捷初始化方法 - 使用依赖注入容器
    init() {
        self._cropViewModel = StateObject(wrappedValue: CropDependencyContainer.cropViewModel())
    }

    var body: some View {
        ScrollView {
            VStack(spacing: 0) {
            // 第一行：复原按钮和操作按钮
                VStack(spacing: screenHeight * 0.03) {  // 主要行之间的间距改为3%
            HStack(spacing: screenWidth * 0.05) {  // 5%间距
                // 复原按钮
                Button(action: {
                    // 重置处理
                    cropViewModel.updateSelectedRatio("original")
                    cropViewModel.resetCropScaleToCenter()
                }) {
                    Text("复原")
                        .font(.system(size: screenHeight * 0.02))
                        .foregroundColor(.white)
                }

                Spacer()

                // 垂直按钮
                Button(action: {
                    // 垂直翻转处理
                }) {
                    Image(systemName: "arrow.up.and.down.righttriangle.up.righttriangle.down")
                        .font(.system(size: screenHeight * 0.02))
                        .foregroundColor(.white)
                }

                // 镜像按钮
                Button(action: {
                    // 镜像处理
                }) {
                    Image(systemName: "arrow.left.and.right.righttriangle.left.righttriangle.right")
                        .font(.system(size: screenHeight * 0.02))
                        .foregroundColor(.white)
                }

                // 旋转按钮
                Button(action: {
                    // 旋转处理
                }) {
                    Image(systemName: "rotate.left")
                        .font(.system(size: screenHeight * 0.02))
                        .foregroundColor(.white)
                }
            }
            .padding(.horizontal, screenWidth * 0.04)
                    .padding(.vertical, screenHeight * 0.01)  // 行内垂直内边距改为1%

            // 第二行：旋转角度刻度条
            VStack {
                // 刻度条
                GeometryReader { geometry in
                    ZStack(alignment: .center) {
                        // 背景区域
                        Rectangle()
                            .fill(Color.clear)
                            .frame(width: screenWidth * 0.92, height: 40)
                            .contentShape(Rectangle())
                            .zIndex(0)

                        // 刻度线层
                        ZStack {
                            // 主内容
                            ZStack {
                                // 刻度线容器
                                HStack(spacing: 0) {
                                    ZStack(alignment: .leading) {
                                        ForEach(-9...9, id: \.self) { i in
                                            let position = CGFloat(i + 9) / 18.0
                                            let xPosition = position * (screenWidth * 0.92) - 0.5
                                            let offset: CGFloat = 0
                                            let mainTickLength: CGFloat = UIScreen.main.bounds.height * UIConstants.dialMainTickLength
                                            let subTickLength: CGFloat = UIScreen.main.bounds.height * UIConstants.dialSubTickLength

                                            Path { path in
                                                let startPoint = CGPoint(x: xPosition, y: offset)
                                                let endPoint = CGPoint(x: xPosition, y: offset + (i % 3 == 0 ? mainTickLength : subTickLength))

                                                path.move(to: startPoint)
                                                path.addLine(to: endPoint)
                                            }
                                            .stroke(i % 3 == 0 ? Color.white : Color.white.opacity(0.4),
                                                   lineWidth: UIConstants.dialTickWidth)
                                        }
                                    }
                                    .frame(width: screenWidth * 0.92)
                                }
                            }
                            .offset(x: cropViewModel.cropScaleDragOffset)

                            // 遮罩视图 - 使用独立的Path
                            MaskUtils.createScaleRulerMaskPath(centerX: geometry.size.width / 2, config: .scaleRuler)
                                .fill(Color.black)
                                .blendMode(.destinationOut)
                        }
                        .compositingGroup()
                        .zIndex(1)

                        // 使用ZStack替代VStack，采用与刻度盘相同的绝对定位
                        ZStack {
                            // 指示器三角形
                            Path { path in
                                let width: CGFloat = UIScreen.main.bounds.width * 0.015
                                let height: CGFloat = UIScreen.main.bounds.height * 0.015
                                let centerX = geometry.size.width / 2

                                // 修正坐标计算，使三角形顶点朝下，使用与刻度盘相同的位置计算
                                let bottomPoint = CGPoint(x: centerX, y: height) // 顶点在下
                                let leftPoint = CGPoint(x: centerX - width/2, y: 0) // 左点在上
                                let rightPoint = CGPoint(x: centerX + width/2, y: 0) // 右点在上

                                path.move(to: bottomPoint)
                                path.addLine(to: leftPoint)
                                path.addLine(to: rightPoint)
                                path.closeSubpath()
                            }
                            .fill(UIConstants.dialIndicatorColor)

                            // 添加角度数值显示，只显示整数，使用绝对定位
                            Text("\(Int(cropViewModel.rotationAngle))°")
                                .font(.system(size: screenHeight * 0.016, weight: .medium))
                                .foregroundColor(UIConstants.dialIndicatorColor)
                                .position(x: geometry.size.width / 2, y: screenHeight * 0.04) // 定位在三角形下方约4%屏幕高度处
                        }
                        .zIndex(3) // 保持z轴层级，确保显示在最上层
                    }
                    .gesture(
                        DragGesture(minimumDistance: 0)
                            .onChanged { gesture in
                                if !cropViewModel.isTouching {
                                    cropViewModel.startLocation = gesture.location
                                    cropViewModel.startDragOffset = cropViewModel.cropScaleDragOffset
                                    cropViewModel.isTouching = true
                                    cropViewModel.onScaleTouchBegan?()
                                }

                                // 计算水平方向的移动距离
                                let translation = gesture.location.x - cropViewModel.startLocation.x

                                // 计算新的偏移量 (直接使用translation而不是取反)
                                let newOffset = cropViewModel.startDragOffset + translation

                                // 限制拖动范围
                                cropViewModel.updateCropScaleOffset(newOffset)
                            }
                            .onEnded { _ in
                                cropViewModel.isTouching = false
                                cropViewModel.lastDragOffset = cropViewModel.cropScaleDragOffset
                                cropViewModel.onScaleTouchEnded?()

                                // 保存偏移量
                                UserDefaults.standard.set(cropViewModel.cropScaleDragOffset, forKey: "LastCropScaleOffset")
                            }
                    )
                    // 使用simultaneousGesture允许同时识别双击和拖动手势
                    .simultaneousGesture(
                        TapGesture(count: 2)
                            .onEnded {
                                // 重置旋转角度为0
                                withAnimation(AnimationConstants.standardSpring) {
                                    cropViewModel.resetCropScaleToCenter()
                                }
                            }
                    )
                }
                .frame(height: screenHeight * 0.04) // 增加高度，从0.02增加到0.04，确保有足够空间显示指示器
            }
            .padding(.horizontal, screenWidth * 0.04)
                    .padding(.vertical, screenHeight * 0.01)  // 行内垂直内边距改为1%

            // 第三行：比例选择
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: screenWidth * 0.06) {
                    // 原始选项
                    VStack(spacing: 4) {
                        // 图标容器，保持与其他比例相同的高度
                        ZStack {
                            Image(systemName: "nosign")
                                .font(.system(size: screenHeight * 0.02))
                        .foregroundColor(.white)
                    }
                        .frame(height: screenHeight * 0.04)

                        Text("原始")
                                .font(.system(size: screenHeight * 0.016))
                        .foregroundColor(.white)
                    }
                    .onTapGesture {
                        cropViewModel.updateSelectedRatio("original")
                    }

                    // 其他比例选项
                    ForEach([
                        ("16:9", "16:9"),       // 宽屏比例
                        ("9:16", "9:16"),       // 宽屏比例（竖版）
                        ("3:2", "3:2"),         // 专业相机比例
                        ("2:3", "2:3"),         // 专业相机比例（竖版）
                        ("4:3", "4:3"),         // 标准比例
                        ("3:4", "3:4"),         // 标准比例（竖版）
                        ("1:1", "1:1"),         // 方形比例
                        ("6:7", "6:7"),         // 竖版比例
                        ("7:6", "7:6"),         // 横版比例
                        ("2:1", "2:1"),         // 宽幅比例
                        ("1:2", "1:2"),         // 宽幅比例（竖版）
                        ("1.85:1", "1.85:1"),   // 电影比例
                        ("2.39:1", "2.39:1"),   // 影院比例
                        ("1.66:1", "1.66:1"),   // 欧洲宽银幕
                        ("XPAN", "XPAN")        // 哈苏XPAN
                    ], id: \.1) { ratio in
                        VStack(spacing: 4) {
                            // 比例形状预览
                            RatioPreviewShape(ratio: ratio.1)
                                .stroke(cropViewModel.selectedRatio == ratio.1 ? UIConstants.dialIndicatorColor : Color.white, lineWidth: 1)
                                .frame(width: screenHeight * 0.04, height: screenHeight * 0.04)

                            // 比例文字
                            Text(ratio.0)
                                .font(.system(size: screenHeight * 0.016))
                                .foregroundColor(cropViewModel.selectedRatio == ratio.1 ? UIConstants.dialIndicatorColor : Color.white)
                        }
                        .onTapGesture {
                            cropViewModel.updateSelectedRatio(ratio.1)
                    }
                }
            }
                .padding(.horizontal, screenWidth * 0.04)
                        .padding(.vertical, screenHeight * 0.01)  // 行内垂直内边距改为1%
                    }
                }
            }
        }
        .safeAreaInset(edge: .bottom) {
            Color.clear.frame(height: screenHeight * 0.06)
        }
        .frame(maxWidth: .infinity)
    }
}

// 比例预览形状
struct RatioPreviewShape: Shape {
    let ratio: String

    func path(in rect: CGRect) -> Path {
        var path = Path()

        // 根据比例计算矩形大小
        let size: CGSize
        switch ratio {
        case "1:1":
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.8)
        case "4:3":
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.6)
        case "3:4":
            size = CGSize(width: rect.width * 0.6, height: rect.width * 0.8)
        case "3:2":
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.533)
        case "2:3":
            size = CGSize(width: rect.width * 0.533, height: rect.width * 0.8)
        case "16:9":
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.45)
        case "9:16":
            size = CGSize(width: rect.width * 0.45, height: rect.width * 0.8)
        case "6:7":
            size = CGSize(width: rect.width * 0.686, height: rect.width * 0.8)
        case "7:6":
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.686)
        case "2:1":
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.4)
        case "1:2":
            size = CGSize(width: rect.width * 0.4, height: rect.width * 0.8)
        case "1.85:1":
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.432)
        case "2.39:1":
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.335)
        case "1.66:1":
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.482)
        case "XPAN":
            // XPAN 的比例接近 65:24 (约2.7:1)
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.296)
        default:
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.8)
        }

        let origin = CGPoint(
            x: (rect.width - size.width) / 2,
            y: (rect.height - size.height) / 2
        )
        path.addRect(CGRect(origin: origin, size: size))

        return path
    }
}
