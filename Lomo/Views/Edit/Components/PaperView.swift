import SwiftUI

/// 相纸控制视图 - MVVM-S架构
struct PaperView: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height

    // MVVM-S架构：使用PaperViewModel
    @StateObject private var paperViewModel: PaperViewModel

    // 初始化方法 - 依赖注入
    init(paperViewModel: PaperViewModel) {
        self._paperViewModel = StateObject(wrappedValue: paperViewModel)
    }

    // 便捷初始化方法 - 使用依赖注入容器
    init() {
        self._paperViewModel = StateObject(wrappedValue: PaperDependencyContainer.paperViewModel())
    }

    // 为了使UI更新流畅，使用中间状态变量
    @State private var selectedPolaroidPreset: Int = 0
    @State private var selectedFilmPreset: Int = 0
    @State private var selectedVintagePreset: Int = 0
    @State private var selectedFashionPreset: Int = 0
    @State private var selectedINSPreset: Int = 0

    var body: some View {
        ScrollView {
            VStack(spacing: 0) {
                // 宝丽来
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    Text("宝丽来")
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)

                    // 预设选择（水平滚动）
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.02) {
                            ForEach(0..<5) { index in
                                Button(action: {
                                    selectedPolaroidPreset = index
                                    paperViewModel.selectPolaroidPreset(index)
                                }) {
                                    VStack(spacing: screenHeight * 0.005) {
                                        Rectangle()
                                            .fill(Color(uiColor: .systemGray5))
                                            .frame(width: screenWidth * 0.15, height: screenHeight * 0.08)
                                            .overlay(alignment: .bottomTrailing) {
                                                if paperViewModel.activePaperType == "polaroid" && paperViewModel.activePaperPresetIndex == index {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }

                                        Text("预设\(index + 1)")
                                            .font(.system(size: screenHeight * 0.012))
                                            .foregroundColor(.white)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.02)
                    }
                }
                .padding(.horizontal, screenWidth * 0.04)
                .padding(.vertical, screenHeight * 0.01)

                // 胶片
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    Text("胶片")
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)

                    // 预设选择（水平滚动）
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.02) {
                            ForEach(0..<5) { index in
                                Button(action: {
                                    selectedFilmPreset = index
                                    paperViewModel.selectFilmPreset(index)
                                }) {
                                    VStack(spacing: screenHeight * 0.005) {
                                        Rectangle()
                                            .fill(Color(uiColor: .systemGray5))
                                            .frame(width: screenWidth * 0.15, height: screenHeight * 0.08)
                                            .overlay(alignment: .bottomTrailing) {
                                                if paperViewModel.activePaperType == "film" && paperViewModel.activePaperPresetIndex == index {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }

                                        Text("预设\(index + 1)")
                                            .font(.system(size: screenHeight * 0.012))
                                            .foregroundColor(.white)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.02)
                    }
                }
                .padding(.horizontal, screenWidth * 0.04)
                .padding(.vertical, screenHeight * 0.01)

                // 复古
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    Text("复古")
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)

                    // 预设选择（水平滚动）
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.02) {
                            ForEach(0..<5) { index in
                                Button(action: {
                                    selectedVintagePreset = index
                                    paperViewModel.selectVintagePreset(index)
                                }) {
                                    VStack(spacing: screenHeight * 0.005) {
                                        Rectangle()
                                            .fill(Color(uiColor: .systemGray5))
                                            .frame(width: screenWidth * 0.15, height: screenHeight * 0.08)
                                            .overlay(alignment: .bottomTrailing) {
                                                if paperViewModel.activePaperType == "vintage" && paperViewModel.activePaperPresetIndex == index {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }

                                        Text("预设\(index + 1)")
                                            .font(.system(size: screenHeight * 0.012))
                                            .foregroundColor(.white)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.02)
                    }
                }
                .padding(.horizontal, screenWidth * 0.04)
                .padding(.vertical, screenHeight * 0.01)

                // 时尚
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    Text("时尚")
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)

                    // 预设选择（水平滚动）
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.02) {
                            ForEach(0..<5) { index in
                                Button(action: {
                                    selectedFashionPreset = index
                                    paperViewModel.selectFashionPreset(index)
                                }) {
                                    VStack(spacing: screenHeight * 0.005) {
                                        Rectangle()
                                            .fill(Color(uiColor: .systemGray5))
                                            .frame(width: screenWidth * 0.15, height: screenHeight * 0.08)
                                            .overlay(alignment: .bottomTrailing) {
                                                if paperViewModel.activePaperType == "fashion" && paperViewModel.activePaperPresetIndex == index {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }

                                        Text("预设\(index + 1)")
                                            .font(.system(size: screenHeight * 0.012))
                                            .foregroundColor(.white)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.02)
                    }
                }
                .padding(.horizontal, screenWidth * 0.04)
                .padding(.vertical, screenHeight * 0.01)

                // INS风
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    Text("INS风")
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)

                    // 预设选择（水平滚动）
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.02) {
                            ForEach(0..<5) { index in
                                Button(action: {
                                    selectedINSPreset = index
                                    paperViewModel.selectINSPreset(index)
                                }) {
                                    VStack(spacing: screenHeight * 0.005) {
                                        Rectangle()
                                            .fill(Color(uiColor: .systemGray5))
                                            .frame(width: screenWidth * 0.15, height: screenHeight * 0.08)
                                            .overlay(alignment: .bottomTrailing) {
                                                if paperViewModel.activePaperType == "ins" && paperViewModel.activePaperPresetIndex == index {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }

                                        Text("预设\(index + 1)")
                                            .font(.system(size: screenHeight * 0.012))
                                            .foregroundColor(.white)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.02)
                    }
                }
                .padding(.horizontal, screenWidth * 0.04)
                .padding(.vertical, screenHeight * 0.01)
            }
        }
        .safeAreaInset(edge: .bottom) {
            Color.clear.frame(height: screenHeight * 0.06)
        }
        .frame(maxWidth: .infinity)
        .onAppear {
            // 设置会在PaperViewModel初始化时自动加载
            // 这里只需要同步UI状态
        }
    }
}
