import SwiftUI

// 特效控制视图组件
struct EffectsView: View {
    @ObservedObject var effectsViewModel: EffectsViewModel
    private let effectManager = EffectsService.shared

    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height

    // 效果参数控制
    @State private var selectedTimeStyle = ""
    @State private var selectedTimeColor = "orange" // 默认橙色
    @State private var selectedTimePosition = "bottomLeft" // 新增状态变量
    @State private var grainIntensity: Double = 0.0
    @State private var scratchIntensity: Double = 0.0
    @State private var leakIntensity: Double = 0.0

    // 时间戳颜色选项
    private let timeColorOptions = [
        ("红色", "red"),
        ("橙色", "orange"),
        ("黄色", "yellow"),
        ("蓝色", "blue"),
        ("紫色", "purple"),
        ("青色", "cyan"),
        ("洋红色", "magenta")
    ]

    // 时间戳位置选项
    private let timePositionOptions = [
        ("bottomLeft", "inset.filled.bottomleft.rectangle.portrait"),
        ("bottomRight", "inset.filled.bottomright.rectangle.portrait"),
        ("topLeft", "inset.filled.topleading.rectangle.portrait"),
        ("topRight", "inset.filled.toptrailing.rectangle.portrait")
    ]

    // 根据预设ID返回对应的颜色
    private func colorForPreset(_ presetId: String) -> Color {
        switch presetId {
        case "warm":
            return Color.orange.opacity(0.8)
        case "cool":
            return Color.blue.opacity(0.6)
        case "vintage":
            return Color(red: 0.8, green: 0.6, blue: 0.4).opacity(UIConstants.effectOpacity)
        case "dramatic":
            return Color.purple.opacity(0.6)
        case "soft":
            return Color.pink.opacity(0.5)
        case "golden":
            return Color.yellow.opacity(UIConstants.effectOpacity)
        default:
            return Color.orange.opacity(0.6)
        }
    }

    var body: some View {
        ScrollView {
            VStack(spacing: 0) {
                // 时间特效
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    HStack {
                        Text("时间")
                            .font(.system(size: screenHeight * 0.02, weight: .bold))
                            .foregroundColor(.white)

                        Spacer()

                        // 时间位置选项按钮
                        HStack(spacing: screenWidth * 0.03) {
                            ForEach(timePositionOptions, id: \.0) { (positionId, iconName) in
                                Button(action: {
                                    selectedTimePosition = positionId
                                    effectManager.updateSetting(\.selectedTimePosition, value: positionId)
                                }) {
                                    Image(systemName: iconName)
                                        .font(.system(size: screenHeight * 0.02))
                                        .foregroundColor(selectedTimePosition == positionId ? UIConstants.dialIndicatorColor : .white)
                                }
                            }
                        }
                    }
                    .padding(.horizontal, screenWidth * 0.04)

                    // 时间样式水平滑动
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.04) {
                            // 点阵数字时钟样式 (第一个选项)
                            Button(action: {
                                // 如果当前已选中，则取消选择
                                if selectedTimeStyle == "digital_orange" {
                                    selectedTimeStyle = ""
                                    effectManager.updateSetting(\.selectedTimeStyle, value: "")
                                } else {
                                    selectedTimeStyle = "digital_orange"
                                    effectManager.updateSetting(\.selectedTimeStyle, value: selectedTimeStyle)
                                }
                            }) {
                                VStack(alignment: .center, spacing: screenHeight * 0.005) {
                                    // 获取组件并使用 HStack 显示
                                    let components = FontUtils.getFormattedDateComponents()
                                    HStack(spacing: 10) { // digital_orange 组件间使用 10 点间距
                                        Text(components.month)
                                        Text(components.day)

                                        // 引号和年份使用嵌套 HStack，间距为 4
                                        HStack(spacing: 4) {
                                            Text("'")
                                            Text(components.year)
                                        }
                                        .padding(.leading, 0)
                                    }
                                    .font(Font.custom(FontUtils.lcdDotFontName, size: screenHeight * 0.0175))
                                    .foregroundColor(selectedTimeColor.isEmpty ? .red : FontUtils.getColorByName(selectedTimeColor))
                                    .padding(.horizontal, screenWidth * 0.02)
                                    .frame(height: screenHeight * 0.04)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(Color.black)
                                            .overlay(alignment: .bottomTrailing) {
                                                if selectedTimeStyle == "digital_orange" {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }
                                    )

                                    // 透明占位符 - 为选中图标提供额外的可见空间
                                    Color.clear
                                        .frame(height: screenHeight * 0.0025)
                                }
                            }

                            // 数字7风格时钟样式 (第二个选项)
                            Button(action: {
                                // 如果当前已选中，则取消选择
                                if selectedTimeStyle == "digital_red" {
                                    selectedTimeStyle = ""
                                    effectManager.updateSetting(\.selectedTimeStyle, value: "")
                                } else {
                                    selectedTimeStyle = "digital_red"
                                    effectManager.updateSetting(\.selectedTimeStyle, value: selectedTimeStyle)
                                }
                            }) {
                                VStack(alignment: .center, spacing: screenHeight * 0.005) {
                                    // 获取组件并使用 HStack 显示
                                    let components = FontUtils.getFormattedDateComponents()
                                    HStack(spacing: 4) { // digital_red 组件间使用 4 点间距（月份与日期之间）
                                        Text(components.month)
                                        Text(components.day)
                                            .padding(.trailing, 2) // 额外添加2点间距，使日期与引号之间总间距为6点

                                        // 引号和年份使用嵌套 HStack，间距为 0
                                        HStack(spacing: 0) {
                                            Text("'")
                                            Text(components.year)
                                        }
                                        .padding(.leading, 0)
                                    }
                                    .font(Font.custom(FontUtils.digital7FontName, size: screenHeight * 0.0225))
                                    .foregroundColor(selectedTimeColor.isEmpty ? .orange : FontUtils.getColorByName(selectedTimeColor))
                                    .padding(.horizontal, screenWidth * 0.02)
                                    .frame(height: screenHeight * 0.04)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(Color.black)
                                            .overlay(alignment: .bottomTrailing) {
                                                if selectedTimeStyle == "digital_red" {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }
                                    )

                                    // 透明占位符 - 为选中图标提供额外的可见空间
                                    Color.clear
                                        .frame(height: screenHeight * 0.0025)
                                }
                            }

                            // Cyber风格时钟样式 (第三个选项)
                            Button(action: {
                                // 如果当前已选中，则取消选择
                                if selectedTimeStyle == "digital_cyber" {
                                    selectedTimeStyle = ""
                                    effectManager.updateSetting(\.selectedTimeStyle, value: "")
                                } else {
                                    selectedTimeStyle = "digital_cyber"
                                    effectManager.updateSetting(\.selectedTimeStyle, value: selectedTimeStyle)
                                }
                            }) {
                                VStack(alignment: .center, spacing: screenHeight * 0.005) {
                                    // 获取组件并使用 HStack 显示
                                    let components = FontUtils.getFormattedDateComponents()
                                    HStack(spacing: 10) { // digital_cyber 组件间使用 10 点间距
                                        Text(components.month)
                                        Text(components.day)

                                        // 引号和年份使用嵌套 HStack，间距为 4
                                        HStack(spacing: 4) {
                                            Text("'")
                                            Text(components.year)
                                        }
                                        .padding(.leading, 0)
                                    }
                                    .font(Font.custom(FontUtils.cyberFontName, size: screenHeight * 0.02))
                                    .foregroundColor(selectedTimeColor.isEmpty ? .blue : FontUtils.getColorByName(selectedTimeColor))
                                    .padding(.horizontal, screenWidth * 0.02)
                                    .frame(height: screenHeight * 0.04)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(Color.black)
                                            .overlay(alignment: .bottomTrailing) {
                                                if selectedTimeStyle == "digital_cyber" {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }
                                    )

                                    // 透明占位符 - 为选中图标提供额外的可见空间
                                    Color.clear
                                        .frame(height: screenHeight * 0.0025)
                                }
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.04)
                    }

                    // 颜色选项
                    HStack(spacing: screenWidth * 0.02) {
                        ForEach(timeColorOptions, id: \.1) { (colorName, colorId) in
                            Button(action: {
                                // 如果当前已选中，则取消选择
                                if selectedTimeColor == colorId {
                                    selectedTimeColor = ""
                                    effectManager.updateSetting(\.selectedTimeColor, value: "")
                                } else {
                                    selectedTimeColor = colorId
                                    effectManager.updateSetting(\.selectedTimeColor, value: colorId)
                                }
                            }) {
                                Text(colorName)
                                    .font(.system(size: screenHeight * 0.015, weight: .medium))
                                    .foregroundColor(selectedTimeColor == colorId ? .black : .white)
                                    .padding(.horizontal, screenWidth * 0.02)
                                    .padding(.vertical, screenHeight * 0.005)
                                    .background(
                                        RoundedRectangle(cornerRadius: 4)
                                            .fill(selectedTimeColor == colorId ? UIConstants.dialIndicatorColor : Color(uiColor: .systemGray5))
                                    )
                            }
                        }
                    }
                    .padding(.top, 0)
                    .padding(.bottom, screenHeight * 0.01)
                    .padding(.horizontal, screenWidth * 0.04)
                }
                .padding(.vertical, screenHeight * 0.01)

                // 颗粒特效
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    HStack {
                        Text("颗粒")
                            .font(.system(size: screenHeight * 0.02, weight: .bold))
                            .foregroundColor(.white)

                        Spacer()

                        Text(String(format: "%.0f%%", grainIntensity * 100))
                            .font(.system(size: screenHeight * 0.02, weight: .medium))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, screenWidth * 0.04)

                    // 强度滑块
                    CustomSlider(
                        value: Binding(
                        get: { grainIntensity },
                        set: { newValue in
                            grainIntensity = newValue
                            effectsViewModel.updateGrainIntensity(newValue)
                        }
                        ),
                        range: 0...1
                    )

                    // 颗粒预设选择
                    HStack(spacing: screenWidth * 0.02) {
                        ForEach(effectsViewModel.availableGrainPresets) { preset in
                            Button(action: {
                                // 如果当前已选中，则取消选择
                                if effectsViewModel.grainParameters.selectedPreset == preset {
                                    effectsViewModel.selectGrainPreset(nil)
                                    effectManager.updateSetting(\.selectedGrainPreset, value: "")
                                } else {
                                    effectsViewModel.selectGrainPreset(preset)
                                    effectManager.updateSetting(\.selectedGrainPreset, value: preset.id)
                                }
                            }) {
                                Text(preset.name)
                                    .font(.system(size: screenHeight * 0.015, weight: .medium))
                                    .foregroundColor(effectsViewModel.grainParameters.selectedPreset == preset ? .black : .white)
                                    .padding(.horizontal, screenWidth * 0.02)
                                    .padding(.vertical, screenHeight * 0.005)
                                    .background(
                                        RoundedRectangle(cornerRadius: 4)
                                            .fill(effectsViewModel.grainParameters.selectedPreset == preset ? UIConstants.dialIndicatorColor : Color(uiColor: .systemGray5))
                                    )
                            }
                        }
                    }
                    .padding(.vertical, screenHeight * 0.01)
                    .padding(.horizontal, screenWidth * 0.04)
                }
                .padding(.vertical, screenHeight * 0.01)

                // 划痕特效
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    HStack {
                        Text("划痕")
                            .font(.system(size: screenHeight * 0.02, weight: .bold))
                            .foregroundColor(.white)

                        Spacer()

                        Text(String(format: "%.0f%%", scratchIntensity * 100))
                            .font(.system(size: screenHeight * 0.02, weight: .medium))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, screenWidth * 0.04)

                    // 强度滑块
                    CustomSlider(
                        value: Binding(
                        get: { scratchIntensity },
                        set: { newValue in
                            scratchIntensity = newValue
                            effectsViewModel.updateScratchIntensity(newValue)
                        }
                        ),
                        range: 0...1
                    )

                    // 划痕预设选择
                    HStack(spacing: screenWidth * 0.02) {
                        ForEach(effectsViewModel.availableScratchPresets) { preset in
                            Button(action: {
                                // 如果当前已选中，则取消选择
                                if effectsViewModel.scratchParameters.selectedPreset == preset {
                                    effectsViewModel.selectScratchPreset(nil)
                                    effectManager.updateSetting(\.selectedScratchPreset, value: "")
                                } else {
                                    effectsViewModel.selectScratchPreset(preset)
                                    effectManager.updateSetting(\.selectedScratchPreset, value: preset.id)
                                }
                            }) {
                                Text(preset.name)
                                    .font(.system(size: screenHeight * 0.015, weight: .medium))
                                    .foregroundColor(effectsViewModel.scratchParameters.selectedPreset == preset ? .black : .white)
                                    .padding(.horizontal, screenWidth * 0.02)
                                    .padding(.vertical, screenHeight * 0.005)
                                    .background(
                                        RoundedRectangle(cornerRadius: 4)
                                            .fill(effectsViewModel.scratchParameters.selectedPreset == preset ? UIConstants.dialIndicatorColor : Color(uiColor: .systemGray5))
                                    )
                            }
                        }
                    }
                    .padding(.vertical, screenHeight * 0.01)
                    .padding(.horizontal, screenWidth * 0.04)
                }
                .padding(.vertical, screenHeight * 0.01)

                // 漏光特效
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    HStack {
                        Text("漏光")
                            .font(.system(size: screenHeight * 0.02, weight: .bold))
                            .foregroundColor(.white)

                        Spacer()

                        Text(String(format: "%.0f%%", leakIntensity * 100))
                            .font(.system(size: screenHeight * 0.02, weight: .medium))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, screenWidth * 0.04)

                    // 强度滑块
                    CustomSlider(
                        value: Binding(
                        get: { leakIntensity },
                        set: { newValue in
                            leakIntensity = newValue
                            effectsViewModel.updateLightLeakIntensity(newValue)
                        }
                        ),
                        range: 0...1
                    )

                    // 漏光预设选择（水平滚动）
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.04) {
                            // 随机按钮
                            Button(action: {
                                // 随机选择一个预设
                                if let randomPreset = effectsViewModel.availableLightLeakPresets.randomElement() {
                                    effectsViewModel.selectLightLeakPreset(randomPreset)
                                }
                            }) {
                                VStack(alignment: .center, spacing: screenHeight * 0.005) {
                                    // 随机按钮预览图
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color(uiColor: .systemGray5))
                                        .frame(width: screenHeight * 0.05, height: screenHeight * 0.05)
                                        .overlay(
                                            Image(systemName: "shuffle")
                                                .font(.system(size: screenHeight * 0.02, weight: .medium))
                                                .foregroundColor(.white)
                                        )

                                    // 预设名称
                                    Text("随机")
                                        .font(.system(size: screenHeight * 0.012, weight: .medium))
                                        .foregroundColor(.white)
                                }
                            }

                            // 其他预设
                            ForEach(effectsViewModel.availableLightLeakPresets) { preset in
                                Button(action: {
                                    // 如果当前已选中，则取消选择
                                    if effectsViewModel.lightLeakParameters.selectedPreset == preset {
                                        effectsViewModel.selectLightLeakPreset(nil)
                                        effectManager.updateSetting(\.selectedLeakPreset, value: "")
                                    } else {
                                        effectsViewModel.selectLightLeakPreset(preset)
                                        effectManager.updateSetting(\.selectedLeakPreset, value: preset.id)
                                    }
                                }) {
                                    VStack(alignment: .center, spacing: screenHeight * 0.005) {
                                        // 预设效果预览图
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(colorForPreset(preset.id))
                                            .frame(width: screenHeight * 0.05, height: screenHeight * 0.05)
                                            .overlay(alignment: .bottomTrailing) {
                                                if effectsViewModel.lightLeakParameters.selectedPreset == preset {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }

                                        // 预设名称
                                        Text(preset.name)
                                            .font(.system(size: screenHeight * 0.012, weight: .medium))
                                            .foregroundColor(.white)
                                    }
                                }
                            }
                        }
                    }
                    .padding(.horizontal, screenWidth * 0.04)
                    .padding(.vertical, screenHeight * 0.01)
                }
                .padding(.vertical, screenHeight * 0.01)
            }
        }
        .safeAreaInset(edge: .bottom) {
            Color.clear.frame(height: screenHeight * 0.06)
        }
        .onAppear {
            // 加载已保存的设置
            let settings = effectManager.getSettings()
            selectedTimeStyle = settings.selectedTimeStyle
            selectedTimeColor = settings.selectedTimeColor
            selectedTimePosition = settings.selectedTimePosition // 加载位置设置
            grainIntensity = settings.grainIntensity
            scratchIntensity = settings.scratchIntensity
            leakIntensity = settings.leakIntensity

            // 确保效果系统已初始化
            effectsViewModel.setupEffects()

            // 加载预设选择
            if let grainPreset = effectsViewModel.availableGrainPresets.first(where: { $0.id == settings.selectedGrainPreset }) {
                effectsViewModel.selectGrainPreset(grainPreset)
            }
            if let scratchPreset = effectsViewModel.availableScratchPresets.first(where: { $0.id == settings.selectedScratchPreset }) {
                effectsViewModel.selectScratchPreset(scratchPreset)
            }
            if let leakPreset = effectsViewModel.availableLightLeakPresets.first(where: { $0.id == settings.selectedLeakPreset }) {
                effectsViewModel.selectLightLeakPreset(leakPreset)
            }
        }
        .onChange(of: grainIntensity) { _ in
            effectManager.updateSetting(\.grainIntensity, value: grainIntensity)
        }
        .onChange(of: scratchIntensity) { _ in
            effectManager.updateSetting(\.scratchIntensity, value: scratchIntensity)
        }
        .onChange(of: leakIntensity) { _ in
            effectManager.updateSetting(\.leakIntensity, value: leakIntensity)
        }
        .frame(maxWidth: .infinity)
    }
}
