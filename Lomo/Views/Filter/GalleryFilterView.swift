import SwiftUI

struct GalleryFilterView: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 使用GalleryFilterViewModel
    @ObservedObject var viewModel: GalleryFilterViewModel
    
    // 添加初始化方法
    init(viewModel: GalleryFilterViewModel) {
        self.viewModel = viewModel
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 使用统一的导航栏组件
            NavigationTopBar(
                selectedTab: $viewModel.selectedCategory,
                tabs: [
                    ("收藏", .favorites, nil),
                    ("胶片", .film, nil),
                    ("宝丽来", .polaroid, nil),
                    ("自然", .nature, nil),
                    ("清新", .fresh, nil),
                    ("复古", .vintage, nil),
                    ("黑白", .blackAndWhite, nil),
                    ("自定义", .custom, nil)
                ],
                onTabSelected: { category in
                    viewModel.loadFilters(for: category)
                }
            )

            // 滤镜列表
            ScrollView {
                VStack(spacing: 0) {
                    ForEach(viewModel.filters) { filter in
                        FilterItemView(filter: filter) { selectedFilter in
                            viewModel.selectFilter(selectedFilter)
                        }
                        .padding(.horizontal, screenWidth * 0.04)
                    }
                }
                .padding(.vertical, screenHeight * 0.01)
            }

            Spacer()
        }
        .background(Color(uiColor: .systemGray6).edgesIgnoringSafeArea(.all))
    }
}

/// 滤镜分类按钮
struct FilterCategoryButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: screenHeight * UIConstants.tabFontSize, weight: isSelected ? .bold : .regular))
                .foregroundColor(.white)
                .opacity(isSelected ? 1 : 0.6)
        }
    }
}

/// 滤镜项视图，显示单个滤镜的信息
struct FilterItemView: View {
    /// 滤镜数据
    let filter: Filter
    
    /// 选择滤镜的回调
    var onSelect: (Filter) -> Void
    
    /// 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        Button(action: {
            onSelect(filter)
        }) {
            HStack(spacing: screenWidth * 0.02) {
                // 左侧信息区域
                VStack(alignment: .leading, spacing: 4) {
                    // 滤镜名称
                    Text(filter.name)
                        .font(.system(size: screenHeight * 0.015))
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                    
                    // 图标和标签
                    HStack(spacing: 6) {
                        // 滤镜类型图标
                        Image(systemName: filter.iconName)
                            .font(.system(size: screenHeight * 0.015))
                            .foregroundColor(.gray)
                        
                        // 滤镜标签
                        Text(filter.label.rawValue)
                            .font(.system(size: screenHeight * 0.015))
                            .foregroundColor(filter.label == .pro ? UIConstants.dialIndicatorColor : .gray)
                    }
                }
                .frame(width: screenWidth * 0.24, alignment: .leading)
                
                // 中间预览区域
                // 注意：实际应用中应使用实际的图片
                Rectangle()
                    .fill(Color.gray.opacity(UIConstants.lightOpacity))
                    .frame(width: screenWidth * UIConstants.filterPreviewWidth, height: screenHeight * UIConstants.filterPreviewHeight)
                    .cornerRadius(6)
                
                // 右侧箭头指示器
                Image(systemName: "chevron.forward")
                    .font(.system(size: screenHeight * 0.018))
                    .foregroundColor(.gray)
                    .frame(width: screenWidth * 0.04, alignment: .trailing)
            }
            .padding(.vertical, screenHeight * 0.02)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct GalleryFilterView_Previews: PreviewProvider {
    static var previews: some View {
        GalleryFilterView(viewModel: GalleryFilterViewModel())
            .background(Color(uiColor: .systemGray6))
    }
}