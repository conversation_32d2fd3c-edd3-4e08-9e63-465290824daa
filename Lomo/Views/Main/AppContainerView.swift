import SwiftUI

// 添加动画风格枚举
enum TransitionStyle {
    case fade                // 淡入淡出
    case slide               // 底部滑入
    case slideHorizontal     // 水平滑入
    case scale               // 缩放过渡
    case rotate3D            // 3D旋转
    case asymmetrical        // 非对称过渡
    case push                // 推出效果
    case move                // 移动效果
    case blur                // 模糊过渡
    case spring              // 弹性动画
    
    // 获取对应的转场效果
    func transition(for isCamera: Bool) -> AnyTransition {
        switch self {
        case .fade:
            return .opacity
            
        case .slide:
            return isCamera ? 
                .move(edge: .bottom).combined(with: .opacity) : 
                .move(edge: .top).combined(with: .opacity)
            
        case .slideHorizontal:
            return isCamera ? 
                .move(edge: .trailing).combined(with: .opacity) : 
                .move(edge: .leading).combined(with: .opacity)
            
        case .scale:
            return .asymmetric(
                insertion: .scale(scale: 1.1).combined(with: .opacity),
                removal: .scale(scale: 0.9).combined(with: .opacity)
            )
            
        case .rotate3D:
            return .modifier(
                active: Rotate3DModifier(angle: isCamera ? -90 : 90),
                identity: Rotate3DModifier(angle: 0)
            )
            
        case .asymmetrical:
            return .asymmetric(
                insertion: .move(edge: .bottom).combined(with: .opacity),
                removal: .move(edge: .leading).combined(with: .opacity)
            )
            
        case .push:
            return .push(from: isCamera ? .bottom : .top)
            
        case .move:
            return .modifier(
                active: MoveModifier(offsetY: isCamera ? 100 : -100, opacity: 0),
                identity: MoveModifier(offsetY: 0, opacity: 1)
            )
            
        case .blur:
            return .modifier(
                active: BlurModifier(radius: 20, opacity: 0),
                identity: BlurModifier(radius: 0, opacity: 1)
            )
            
        case .spring:
            return .modifier(
                active: SpringModifier(offsetY: isCamera ? 50 : -50, scale: 0.9, opacity: 0),
                identity: SpringModifier(offsetY: 0, scale: 1.0, opacity: 1)
            )
        }
    }
}

// 添加3D旋转修饰符
struct Rotate3DModifier: ViewModifier {
    let angle: Double
    
    func body(content: Content) -> some View {
        content
            .rotation3DEffect(
                .degrees(angle),
                axis: (x: 0.0, y: 1.0, z: 0.0)
            )
    }
}

// 添加移动修饰符
struct MoveModifier: ViewModifier {
    let offsetY: CGFloat
    let opacity: Double
    
    func body(content: Content) -> some View {
        content
            .offset(y: offsetY)
            .opacity(opacity)
    }
}

// 添加模糊修饰符
struct BlurModifier: ViewModifier {
    let radius: CGFloat
    let opacity: Double
    
    func body(content: Content) -> some View {
        content
            .blur(radius: radius)
            .opacity(opacity)
    }
}

// 添加弹性修饰符
struct SpringModifier: ViewModifier {
    let offsetY: CGFloat
    let scale: CGFloat
    let opacity: Double
    
    func body(content: Content) -> some View {
        content
            .offset(y: offsetY)
            .scaleEffect(scale)
            .opacity(opacity)
    }
}

struct AppContainerView: View {
    @ObservedObject var cameraViewModel: CameraViewModel
    @StateObject private var sharedTabViewModel = SharedTabViewModel()
    
    // 选择动画风格 (可以根据需要更改)
    private let transitionStyle: TransitionStyle = .slide
    
    // 从 CameraView 复制的其他 ViewModel 声明
    @StateObject private var filterViewModel = GalleryFilterViewModel()
    @StateObject private var editViewModel = EditViewModel()
    @StateObject private var galleryViewModel = GalleryDependencyContainer.galleryViewModel()
    
    var body: some View {
        ZStack {
            // 如果共享标签页可见，显示它
            if sharedTabViewModel.isVisible {
                SharedTabView(
                    viewModel: sharedTabViewModel,
                    cameraViewModel: cameraViewModel,
                    filterViewModel: filterViewModel,
                    editViewModel: editViewModel,
                    galleryViewModel: galleryViewModel
                )
                .statusBar(hidden: false) // 明确显示状态栏
                .transition(transitionStyle.transition(for: false))
            } 
            // 如果共享标签页不可见，显示相机视图
            else {
                CameraView(
                    viewModel: cameraViewModel, 
                    sharedTabViewModel: sharedTabViewModel
                )
                .statusBar(hidden: true) // 明确隐藏状态栏
                .transition(transitionStyle.transition(for: true))
            }
        }
        // 使用与SharedTabViewModel相同的动画参数
        .animation(.easeInOut(duration: AnimationConstants.duration), value: sharedTabViewModel.isVisible)
    }
} 