import Foundation
import UIKit
import Metal

/// Metal特殊效果测试
class MetalSpecialEffectsTest {
    
    /// 测试Metal特殊效果引擎初始化
    static func testEngineInitialization() {
        print("🧪 [MetalSpecialEffectsTest] 测试Metal特殊效果引擎初始化...")
        
        do {
            let engine = try MetalSpecialEffectsEngine()
            print("✅ [MetalSpecialEffectsTest] Metal特殊效果引擎初始化成功")
        } catch {
            print("❌ [MetalSpecialEffectsTest] Metal特殊效果引擎初始化失败: \(error)")
        }
    }
    
    /// 测试特殊效果着色器编译
    static func testShaderCompilation() {
        print("🧪 [MetalSpecialEffectsTest] 测试特殊效果着色器编译...")
        
        guard let device = MTLCreateSystemDefaultDevice() else {
            print("❌ [MetalSpecialEffectsTest] Metal设备不可用")
            return
        }
        
        guard let library = device.makeDefaultLibrary() else {
            print("❌ [MetalSpecialEffectsTest] 无法加载Metal库")
            return
        }
        
        // 测试特殊效果着色器函数
        let shaderFunctions = [
            "apply_light_leak_effect",
            "apply_grain_effect",
            "apply_scratch_effect",
            "apply_gaussian_blur",
            "apply_dissolve_transition"
        ]
        
        for functionName in shaderFunctions {
            if let function = library.makeFunction(name: functionName) {
                print("✅ [MetalSpecialEffectsTest] 着色器函数可用: \(functionName)")
                
                // 尝试创建计算管线状态
                do {
                    let pipelineState = try device.makeComputePipelineState(function: function)
                    print("✅ [MetalSpecialEffectsTest] 管线状态创建成功: \(functionName)")
                } catch {
                    print("❌ [MetalSpecialEffectsTest] 管线状态创建失败: \(functionName) - \(error)")
                }
            } else {
                print("❌ [MetalSpecialEffectsTest] 着色器函数不可用: \(functionName)")
            }
        }
    }
    
    /// 测试漏光效果服务
    static func testLightLeakService() {
        print("🧪 [MetalSpecialEffectsTest] 测试漏光效果服务...")
        
        let service = LightLeakService.shared
        
        // 创建测试图像
        let testImage = createTestImage()
        
        // 创建测试参数
        var parameters = LightLeakParameters()
        parameters.isEnabled = true
        parameters.intensity = 0.5
        parameters.rotation = 0.0
        parameters.positionOffset = CGPoint(x: 0.1, y: 0.1)
        parameters.selectedPreset = LightLeakPreset.allPresets.first
        
        // 测试应用漏光效果
        let result = service.applyLightLeak(to: testImage, with: parameters)
        
        if result != testImage {
            print("✅ [MetalSpecialEffectsTest] 漏光效果应用成功")
        } else {
            print("⚠️ [MetalSpecialEffectsTest] 漏光效果未应用或失败")
        }
    }
    
    /// 测试颗粒效果服务
    static func testGrainService() {
        print("🧪 [MetalSpecialEffectsTest] 测试颗粒效果服务...")
        
        let service = GrainService.shared
        
        // 创建测试图像
        let testImage = createTestImage()
        
        // 创建测试参数
        var parameters = GrainParameters()
        parameters.isEnabled = true
        parameters.intensity = 0.5
        parameters.selectedPreset = service.getAllGrainPresets().first
        
        // 测试应用颗粒效果
        let result = service.applyGrain(to: testImage, with: parameters)
        
        if result != testImage {
            print("✅ [MetalSpecialEffectsTest] 颗粒效果应用成功")
        } else {
            print("⚠️ [MetalSpecialEffectsTest] 颗粒效果未应用或失败")
        }
    }
    
    /// 测试划痕效果服务
    static func testScratchService() {
        print("🧪 [MetalSpecialEffectsTest] 测试划痕效果服务...")
        
        let service = ScratchService.shared
        
        // 创建测试图像
        let testImage = createTestImage()
        
        // 创建测试参数
        var parameters = ScratchParameters()
        parameters.isEnabled = true
        parameters.intensity = 0.5
        parameters.selectedPreset = ScratchPreset.allPresets.first
        
        // 测试应用划痕效果
        let result = service.applyScratch(to: testImage, with: parameters)
        
        if result != testImage {
            print("✅ [MetalSpecialEffectsTest] 划痕效果应用成功")
        } else {
            print("⚠️ [MetalSpecialEffectsTest] 划痕效果未应用或失败")
        }
    }
    
    /// 测试Metal引擎直接调用
    static func testDirectMetalEngine() {
        print("🧪 [MetalSpecialEffectsTest] 测试Metal引擎直接调用...")
        
        do {
            let engine = try MetalSpecialEffectsEngine()
            let testImage = createTestImage()
            
            // 测试高斯模糊
            let blurResult = try engine.applyGaussianBlur(to: testImage, radius: 5.0)
            print("✅ [MetalSpecialEffectsTest] 高斯模糊测试成功")
            
            // 测试溶解过渡
            let dissolveResult = try engine.applyDissolveTransition(
                from: testImage, 
                to: testImage, 
                progress: 0.5
            )
            print("✅ [MetalSpecialEffectsTest] 溶解过渡测试成功")
            
        } catch {
            print("❌ [MetalSpecialEffectsTest] Metal引擎直接调用失败: \(error)")
        }
    }
    
    /// 创建测试图像
    private static func createTestImage() -> UIImage {
        let size = CGSize(width: 100, height: 100)
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            // 创建渐变背景
            let colors = [UIColor.red.cgColor, UIColor.blue.cgColor]
            let gradient = CGGradient(colorsSpace: CGColorSpaceCreateDeviceRGB(), 
                                    colors: colors as CFArray, 
                                    locations: nil)!
            
            context.cgContext.drawLinearGradient(
                gradient,
                start: CGPoint(x: 0, y: 0),
                end: CGPoint(x: size.width, y: size.height),
                options: []
            )
        }
    }
    
    /// 运行所有测试
    static func runAllTests() {
        TestUtils.printTestStart("MetalSpecialEffectsTest")

        testEngineInitialization()
        TestUtils.printTestGroup("着色器编译")

        testShaderCompilation()
        TestUtils.printTestGroup("漏光效果")

        testLightLeakService()
        TestUtils.printTestGroup("颗粒效果")

        testGrainService()
        TestUtils.printTestGroup("划痕效果")

        testScratchService()
        TestUtils.printTestGroup("Metal引擎直接调用")

        testDirectMetalEngine()

        TestUtils.printTestEnd("MetalSpecialEffectsTest")
    }
}


