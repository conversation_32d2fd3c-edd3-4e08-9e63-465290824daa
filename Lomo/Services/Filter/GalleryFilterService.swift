import Foundation

/// 相册滤镜服务 - MVVM-S架构
class GalleryFilterService: FilterServiceProtocol {
    
    /// 滤镜数据（模拟数据，实际应用中可能从API或本地数据库加载）
    private var filters: [Filter] = [
        // 胶片滤镜
        Filter(name: "Gold 200", type: .film, label: .free, iconName: "film.fill", previewImageName: "gold200_preview", isFavorite: true),
        Filter(name: "ColorPlus 200", type: .film, label: .free, iconName: "film.fill", previewImageName: "colorplus_preview"),
        Filter(name: "Portra 160", type: .film, label: .free, iconName: "film.fill", previewImageName: "portra160_preview"),
        Filter(name: "Natura 1600", type: .film, label: .pro, iconName: "film.fill", previewImageName: "natura1600_preview"),

        // 宝丽来滤镜
        Filter(name: "Polaroid 600", type: .polaroid, label: .free, iconName: "camera.fill", previewImageName: "polaroid600_preview", isFavorite: true),
        Filter(name: "Polaroid SX-70", type: .polaroid, label: .pro, iconName: "camera.fill", previewImageName: "polaroidsx70_preview"),

        // 自然滤镜
        Filter(name: "清晨", type: .nature, label: .free, iconName: "sunrise.fill", previewImageName: "morning_preview"),
        Filter(name: "山间", type: .nature, label: .free, iconName: "mountain.2.fill", previewImageName: "mountain_preview"),
        Filter(name: "海洋", type: .nature, label: .pro, iconName: "water.waves", previewImageName: "ocean_preview", isFavorite: true),

        // 清新滤镜
        Filter(name: "少女粉", type: .fresh, label: .free, iconName: "sparkles", previewImageName: "pink_preview"),
        Filter(name: "薄荷绿", type: .fresh, label: .free, iconName: "leaf.fill", previewImageName: "mint_preview"),

        // 复古滤镜
        Filter(name: "80年代", type: .vintage, label: .free, iconName: "clock.arrow.circlepath", previewImageName: "80s_preview"),
        Filter(name: "复古褐", type: .vintage, label: .pro, iconName: "clock.arrow.circlepath", previewImageName: "sepia_preview"),

        // 黑白滤镜
        Filter(name: "经典黑白", type: .blackAndWhite, label: .free, iconName: "circle.righthalf.filled", previewImageName: "bw_classic_preview", isFavorite: true),
        Filter(name: "高反差", type: .blackAndWhite, label: .pro, iconName: "circle.righthalf.filled", previewImageName: "bw_contrast_preview")
    ]
    
    /// 获取所有滤镜
    func getAllFilters() -> [Filter] {
        return filters
    }
    
    /// 根据类型获取滤镜
    func getFilters(byType type: FilterType) -> [Filter] {
        return filters.filter { $0.type == type }
    }
    
    /// 获取收藏的滤镜
    func getFavoriteFilters() -> [Filter] {
        return filters.filter { $0.isFavorite }
    }
    
    /// 切换滤镜收藏状态
    func toggleFavorite(filterId: String) -> Bool {
        guard let index = filters.firstIndex(where: { $0.id == filterId }) else {
            return false
        }
        
        // 修改收藏状态
        filters[index].isFavorite.toggle()
        return filters[index].isFavorite
    }
}