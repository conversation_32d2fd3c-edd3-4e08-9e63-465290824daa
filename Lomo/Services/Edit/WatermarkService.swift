import Foundation
import SwiftData
import SwiftUI
import UIKit

// MARK: - 水印服务协议
/// 水印业务服务协议，定义所有水印相关的业务操作
/// 这是一个纯包装器协议，完全映射现有的WatermarkSettingsManager功能
protocol WatermarkServiceProtocol {
    // MARK: - 设置管理（完全映射WatermarkSettingsManager的方法）
    func getSettings() -> WatermarkSettings
    func saveSettings(_ settings: WatermarkSettings)
    func updateSetting<T>(_ keyPath: WritableKeyPath<WatermarkSettings, T>, value: T)
    func resetToDefaults()
}

// MARK: - 水印服务实现
/// 水印业务服务实现类
/// **真正重构：直接操作SwiftData，彻底移除Manager依赖**
/// **这是真正的MVVM-S架构实现**
class WatermarkService: WatermarkServiceProtocol {

    // MARK: - 私有属性
    /// SwiftData容器
    private let container: ModelContainer

    /// ModelContext（用于数据操作）
    @MainActor
    private var modelContext: ModelContext {
        return container.mainContext
    }

    /// 同步获取ModelContext（创建新的上下文以避免主线程隔离问题）
    private func getModelContextSync() -> ModelContext {
        // 创建一个新的 ModelContext 而不是使用 mainContext
        // 这样可以避免主线程隔离问题
        return ModelContext(container)
    }

    // MARK: - 初始化
    /// 初始化水印服务
    /// - Parameter container: SwiftData容器
    init(container: ModelContainer) {
        self.container = container
        print("🏭 [WatermarkService] 初始化水印服务（真正的SwiftData操作）")
    }

    /// 便利初始化方法（使用共享容器）
    convenience init() {
        // 使用SharedService的容器
        self.init(container: SharedService.shared.container)
    }

    // MARK: - 设置管理（真正的SwiftData实现）

    /// 获取水印设置
    /// **真正实现：直接从SwiftData获取，不依赖Manager**
    func getSettings() -> WatermarkSettings {
        print("📖 [WatermarkService] getSettings() - 直接从SwiftData获取")

        // 使用同步方式获取ModelContext并执行操作
        let context = getModelContextSync()

        do {
            let descriptor = FetchDescriptor<WatermarkSettings>(
                predicate: #Predicate { $0.id == "watermark_settings" }
            )
            let existingSettings = try context.fetch(descriptor)

            if let settings = existingSettings.first {
                print("✅ [WatermarkService] 成功获取现有设置，水印类型: \(settings.activeWatermarkStyleType)")
                return settings
            } else {
                print("🆕 [WatermarkService] 未找到设置，创建默认设置")
                let newSettings = WatermarkSettings()
                context.insert(newSettings)

                do {
                    try context.save()
                    print("✅ [WatermarkService] 默认设置已保存")
                } catch {
                    print("❌ [WatermarkService] 保存默认设置失败: \(error.localizedDescription)")
                }
                return newSettings
            }
        } catch {
            print("❌ [WatermarkService] 获取设置失败: \(error.localizedDescription)")
            return WatermarkSettings() // 返回临时设置，避免崩溃
        }
    }

    /// 保存水印设置
    /// **真正实现：直接保存到SwiftData，不依赖Manager**
    /// - Parameter settings: 要保存的设置
    func saveSettings(_ settings: WatermarkSettings) {
        print("� [WatermarkService] saveSettings() - 直接保存到SwiftData")

        let saveOperation = {
            let context = self.getModelContextSync()
            do {
                settings.updateTimestamp()
                try context.save()
                print("✅ [WatermarkService] 设置保存成功")
            } catch {
                print("❌ [WatermarkService] 保存设置失败: \(error.localizedDescription)")
            }
        }

        if Thread.isMainThread {
            saveOperation()
        } else {
            DispatchQueue.main.sync(execute: saveOperation)
        }
    }

    /// 更新特定设置
    /// **真正实现：直接操作SwiftData，不依赖Manager**
    /// - Parameters:
    ///   - keyPath: 设置的键路径
    ///   - value: 新值
    func updateSetting<T>(_ keyPath: WritableKeyPath<WatermarkSettings, T>, value: T) {
        print("🔄 [WatermarkService] updateSetting() - 直接更新SwiftData")

        var settings = getSettings() // 获取当前设置
        let oldValue = "\(settings[keyPath: keyPath])"
        settings[keyPath: keyPath] = value

        print("🔄 [WatermarkService] 更新 \(String(describing: keyPath)): '\(oldValue)' -> '\(value)'")
        saveSettings(settings)

        // 如果更新的是水印类型，发送通知（保持兼容性）
        if keyPath == \WatermarkSettings.activeWatermarkStyleType {
            NotificationCenter.default.post(
                name: Notification.Name("WatermarkTypeChanged"),
                object: value
            )
            print("📢 [WatermarkService] 发送水印类型变更通知: \(value)")
        }
    }

    /// 重置为默认设置
    /// **真正实现：直接操作SwiftData，不依赖Manager**
    func resetToDefaults() {
        print("🔄 [WatermarkService] resetToDefaults() - 直接重置SwiftData")

        let resetOperation = {
            let context = self.getModelContextSync()
            do {
                // 删除所有现有设置
                let descriptor = FetchDescriptor<WatermarkSettings>()
                let existingSettings = try context.fetch(descriptor)

                for settings in existingSettings {
                    context.delete(settings)
                }

                // 创建新的默认设置
                let newSettings = WatermarkSettings()
                context.insert(newSettings)
                try context.save()

                print("✅ [WatermarkService] 重置为默认设置成功")
            } catch {
                print("❌ [WatermarkService] 重置设置失败: \(error.localizedDescription)")
            }
        }

        if Thread.isMainThread {
            resetOperation()
        } else {
            DispatchQueue.main.sync(execute: resetOperation)
        }
    }
}

// MARK: - 扩展方法（为未来准备，暂时不使用）
extension WatermarkService {

    /// 获取可用的水印类型列表
    /// **注意：这个方法暂时不实现，保持现有的硬编码逻辑**
    func getAvailableWatermarkTypes() -> [String] {
        // 暂时返回空数组，保持现有逻辑不变
        return []
    }

    /// 应用水印到容器视图
    /// **注意：这个方法暂时不实现，保持现有的WatermarkManagerProvider逻辑**
    /// - Parameters:
    ///   - styleType: 水印样式类型
    ///   - container: 容器视图
    func applyWatermark(_ styleType: String, to container: UIView) {
        // 暂时不实现，保持现有逻辑不变
        print("🎨 [WatermarkService] applyWatermark() - 暂时不实现，保持现有逻辑")
    }

    /// 移除水印
    /// **注意：这个方法暂时不实现，保持现有的WatermarkManagerProvider逻辑**
    /// - Parameter container: 容器视图
    func removeWatermark(from container: UIView) {
        // 暂时不实现，保持现有逻辑不变
        print("🧹 [WatermarkService] removeWatermark() - 暂时不实现，保持现有逻辑")
    }
}