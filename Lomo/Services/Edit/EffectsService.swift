import Foundation
import UIKit
import SwiftData
import SwiftUI

/// 特效服务 - 集中管理和应用各种特效
class EffectsService {
    // MARK: - 单例
    static let shared = EffectsService()
    
    // MARK: - 依赖
    private let lightLeakService = LightLeakService.shared
    private let grainService = GrainService.shared
    private let scratchService = ScratchService.shared
    private let notificationService = NotificationService.shared

    // 模型容器和上下文
    private var modelContainer: ModelContainer?
    private var modelContext: ModelContext?
    
    // MARK: - 特效参数
    
    // 漏光效果参数
    private var lightLeakParameters = LightLeakParameters.default
    private var availableLightLeakPresets: [LightLeakPreset] = []
    private var selectedLightLeakPreset: LightLeakPreset?
    
    // 颗粒效果参数
    private var grainParameters = GrainParameters.default
    private var availableGrainPresets: [GrainPreset] = []
    private var selectedGrainPreset: GrainPreset?
    
    // 划痕效果参数
    private var scratchParameters = ScratchParameters.default
    private var availableScratchPresets: [ScratchPreset] = []
    private var selectedScratchPreset: ScratchPreset?
    
    // MARK: - 初始化
    private init() {
        setupModelContainer()
        setupEffects()
    }
    
    // MARK: - 公共属性访问方法
    
    // 获取漏光效果参数
    func getLightLeakParameters() -> LightLeakParameters {
        return lightLeakParameters
    }
    
    // 设置漏光效果参数
    func setLightLeakParameters(_ parameters: LightLeakParameters) {
        lightLeakParameters = parameters
    }
    
    // 获取颗粒效果参数
    func getGrainParameters() -> GrainParameters {
        return grainParameters
    }
    
    // 设置颗粒效果参数
    func setGrainParameters(_ parameters: GrainParameters) {
        grainParameters = parameters
    }
    
    // 获取划痕效果参数
    func getScratchParameters() -> ScratchParameters {
        return scratchParameters
    }
    
    // 设置划痕效果参数
    func setScratchParameters(_ parameters: ScratchParameters) {
        scratchParameters = parameters
    }
    
    // 获取可用的漏光预设
    func getAvailableLightLeakPresets() -> [LightLeakPreset] {
        return availableLightLeakPresets
    }
    
    // 获取可用的颗粒预设
    func getAvailableGrainPresets() -> [GrainPreset] {
        return availableGrainPresets
    }
    
    // 获取可用的划痕预设
    func getAvailableScratchPresets() -> [ScratchPreset] {
        return availableScratchPresets
    }
    
    // MARK: - 初始化特效系统
    
    /// 初始化特效系统
    private func setupEffects() {
        // 加载所有可用的漏光预设
        loadLightLeakPresets()
        
        // 加载所有可用的颗粒预设
        loadGrainPresets()
        
        // 加载所有可用的划痕预设
        loadScratchPresets()
    }
    
    // MARK: - 漏光效果方法
    
    /// 加载所有可用的漏光预设
    private func loadLightLeakPresets() {
        availableLightLeakPresets = lightLeakService.getAllLightLeakPresets()
    }
    
    /// 选择漏光预设
    /// - Parameter preset: 要选择的预设，如果为nil则取消选择
    func selectLightLeakPreset(_ preset: LightLeakPreset?) {
        // 更新参数中的预设
        lightLeakParameters.selectedPreset = preset
        selectedLightLeakPreset = preset
        
        // 如果没有选择预设，则禁用漏光效果
        if preset == nil {
            lightLeakParameters.isEnabled = false
        } else {
            // 如果选择了预设但漏光效果未启用，自动启用
            if !lightLeakParameters.isEnabled {
                lightLeakParameters.isEnabled = true
            }
        }
        
        // 通知效果更新
        notifyEffectsUpdated()
    }
    
    /// 更新漏光强度
    /// - Parameter intensity: 新的强度值 (0.0-1.0)
    func updateLightLeakIntensity(_ intensity: Double) {
        lightLeakParameters.intensity = intensity
        notifyEffectsUpdated()
    }
    
    /// 更新漏光混合模式
    /// - Parameter blendMode: 新的混合模式
    func updateLightLeakBlendMode(_ blendMode: LightLeakBlendMode) {
        lightLeakParameters.blendMode = blendMode
        notifyEffectsUpdated()
    }
    
    /// 更新漏光旋转角度
    /// - Parameter rotation: 旋转角度（弧度）
    func updateLightLeakRotation(_ rotation: Double) {
        lightLeakParameters.rotation = rotation
        notifyEffectsUpdated()
    }
    
    /// 更新漏光位置偏移
    /// - Parameter offset: 位置偏移
    func updateLightLeakPosition(_ offset: CGPoint) {
        lightLeakParameters.positionOffset = offset
        notifyEffectsUpdated()
    }
    
    /// 切换漏光效果开启/关闭状态
    func toggleLightLeakEnabled() {
        lightLeakParameters.isEnabled.toggle()
        notifyEffectsUpdated()
    }
    
    /// 重置漏光效果参数
    func resetLightLeakParameters() {
        lightLeakParameters = LightLeakParameters.default
        selectedLightLeakPreset = nil
        notifyEffectsUpdated()
    }
    
    // MARK: - 颗粒效果方法
    
    /// 加载所有可用的颗粒预设
    private func loadGrainPresets() {
        availableGrainPresets = grainService.getAllGrainPresets()
    }
    
    /// 选择颗粒预设
    /// - Parameter preset: 要选择的预设，如果为nil则取消选择
    func selectGrainPreset(_ preset: GrainPreset?) {
        // 更新参数中的预设
        grainParameters.selectedPreset = preset
        selectedGrainPreset = preset
        
        // 如果没有选择预设，则禁用颗粒效果
        if preset == nil {
            grainParameters.isEnabled = false
        } else {
            // 如果选择了预设但颗粒效果未启用，自动启用
            if !grainParameters.isEnabled {
                grainParameters.isEnabled = true
            }
        }
        
        notifyEffectsUpdated()
    }
    
    /// 更新颗粒强度
    /// - Parameter intensity: 新的强度值 (0.0-1.0)
    func updateGrainIntensity(_ intensity: Double) {
        grainParameters.intensity = intensity
        notifyEffectsUpdated()
    }
    
    /// 切换颗粒效果开启/关闭状态
    func toggleGrainEnabled() {
        grainParameters.isEnabled.toggle()
        notifyEffectsUpdated()
    }
    
    /// 重置颗粒效果参数
    func resetGrainParameters() {
        grainParameters = GrainParameters.default
        selectedGrainPreset = nil
        notifyEffectsUpdated()
    }
    
    // MARK: - 划痕效果方法
    
    /// 加载所有可用的划痕预设
    private func loadScratchPresets() {
        availableScratchPresets = scratchService.getAllScratchPresets()
    }
    
    /// 选择划痕预设
    /// - Parameter preset: 要选择的预设，如果为nil则取消选择
    func selectScratchPreset(_ preset: ScratchPreset?) {
        // 更新参数中的预设
        scratchParameters.selectedPreset = preset
        selectedScratchPreset = preset
        
        // 如果没有选择预设，则禁用划痕效果
        if preset == nil {
            scratchParameters.isEnabled = false
        } else {
            // 如果选择了预设但划痕效果未启用，自动启用
            if !scratchParameters.isEnabled {
                scratchParameters.isEnabled = true
            }
        }
        
        notifyEffectsUpdated()
    }
    
    /// 更新划痕强度
    /// - Parameter intensity: 新的强度值 (0.0-1.0)
    func updateScratchIntensity(_ intensity: Double) {
        scratchParameters.intensity = intensity
        notifyEffectsUpdated()
    }
    
    /// 切换划痕效果开启/关闭状态
    func toggleScratchEnabled() {
        scratchParameters.isEnabled.toggle()
        notifyEffectsUpdated()
    }
    
    /// 重置划痕效果参数
    func resetScratchParameters() {
        scratchParameters = ScratchParameters.default
        selectedScratchPreset = nil
        notifyEffectsUpdated()
    }
    
    // MARK: - 通用特效方法
    
    /// 通知特效更新
    func notifyEffectsUpdated() {
        // 发送通知，表示特效已更新
        notificationService.post(name: .effectsDidUpdate, object: nil)
    }
    
    /// 应用漏光效果到拍摄的照片
    /// - Parameter image: 原始图像
    /// - Returns: 应用了漏光效果的图像
    func applyLightLeakToImage(_ image: UIImage) -> UIImage {
        return lightLeakService.applyLightLeak(to: image, with: lightLeakParameters)
    }
    
    /// 应用颗粒效果到拍摄的照片
    /// - Parameter image: 原始图像
    /// - Returns: 应用了颗粒效果的图像
    func applyGrainToImage(_ image: UIImage) -> UIImage {
        return grainService.applyGrain(to: image, with: grainParameters)
    }
    
    /// 应用划痕效果到拍摄的照片
    /// - Parameter image: 原始图像
    /// - Returns: 应用了划痕效果的图像
    func applyScratchToImage(_ image: UIImage) -> UIImage {
        return scratchService.applyScratch(to: image, with: scratchParameters)
    }
    
    /// 应用所有特效到拍摄的照片
    /// - Parameter image: 原始图像
    /// - Returns: 应用了所有特效的图像
    func applyAllEffectsToImage(_ image: UIImage) -> UIImage {
        var processedImage = image
        
        // 应用漏光效果
        if lightLeakParameters.isEnabled {
            processedImage = applyLightLeakToImage(processedImage)
        }
        
        // 应用颗粒效果
        if grainParameters.isEnabled {
            processedImage = applyGrainToImage(processedImage)
        }
        
        // 应用划痕效果
        if scratchParameters.isEnabled {
            processedImage = applyScratchToImage(processedImage)
        }
        
        return processedImage
    }
    
    /// 重置所有特效
    func resetAllEffects() {
        resetLightLeakParameters()
        resetGrainParameters()
        resetScratchParameters()
    }

    // MARK: - 数据持久化方法（从EffectSettingsManager添加）

    // 设置SwiftData模型容器
    private func setupModelContainer() {
        // 使用共享容器替代单独创建容器
        self.modelContainer = SharedService.shared.container
        if let container = self.modelContainer {
            self.modelContext = ModelContext(container)
            print(" EffectsService: 已使用共享 ModelContainer 和 ModelContext。")
        } else {
            print(" EffectsService: 获取共享 ModelContainer 失败！")
        }
    }

    /// 获取特效设置
    func getSettings() -> EffectsModel {
        guard let context = modelContext else {
            return EffectsModel()
        }

        do {
            // 尝试获取现有设置
            let descriptor = FetchDescriptor<EffectsModel>(predicate: #Predicate { $0.id == "effect_settings" })
            let existingSettings = try context.fetch(descriptor)

            // 如果存在设置，返回第一个
            if let settings = existingSettings.first {
                return settings
            }

            // 如果不存在，创建新的设置并保存
            let newSettings = EffectsModel()
            context.insert(newSettings)
            try context.save()
            return newSettings
        } catch {
            print("获取特效设置失败: \(error.localizedDescription)")
            // 发生错误时返回默认设置
            return EffectsModel()
        }
    }

    /// 保存设置
    func saveSettings(_ settings: EffectsModel) {
        guard let context = modelContext else {
            print("保存失败：模型上下文不可用")
            return
        }

        do {
            // 更新时间戳
            settings.updateTimestamp()
            // 保存上下文中的更改
            try context.save()
        } catch {
            print("保存特效设置失败: \(error.localizedDescription)")
        }
    }

    /// 更新特定设置
    func updateSetting<T>(_ keyPath: WritableKeyPath<EffectsModel, T>, value: T) {
        var settings = getSettings()
        settings[keyPath: keyPath] = value
        saveSettings(settings)
    }

    /// 随机生成新的漏光效果
    func randomizeLeakEffect() {
        var settings = getSettings()
        settings.randomizeLeakEffect()
        saveSettings(settings)
    }

    /// 重置所有设置为默认值
    func resetToDefaults() {
        guard let context = modelContext else {
            return
        }

        do {
            // 删除所有现有设置
            let descriptor = FetchDescriptor<EffectsModel>()
            let existingSettings = try context.fetch(descriptor)
            for settings in existingSettings {
                context.delete(settings)
            }

            // 创建新的默认设置
            let newSettings = EffectsModel()
            context.insert(newSettings)
            try context.save()
        } catch {
            print("重置特效设置失败: \(error.localizedDescription)")
        }
    }
}

// MARK: - 通知名称扩展
extension Notification.Name {
    /// 特效更新通知
    static let effectsDidUpdate = Notification.Name("com.lomo.effects.didUpdate")
} 