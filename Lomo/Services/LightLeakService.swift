import Foundation
import UIKit
import Metal

/// 漏光效果服务 - 使用Metal实现
class LightLeakService {
    // 单例模式
    static let shared = LightLeakService()

    // 私有初始化方法，确保单例实现
    private init() {
        loadPresets()
        print("🎨 [LightLeakService] 初始化 - 使用Metal实现")
    }

    // Metal特殊效果引擎
    private lazy var metalEngine: MetalSpecialEffectsEngine? = {
        do {
            return try MetalSpecialEffectsEngine()
        } catch {
            print("❌ [LightLeakService] Metal引擎初始化失败: \(error)")
            return nil
        }
    }()

    // 预设缓存，避免重复加载
    private var presetCache: [String: UIImage] = [:]

    // 加载预设资源
    private func loadPresets() {
        // 实际开发中，这里会预加载所有漏光预设到缓存
        // 目前为模拟实现
    }
    
    // MARK: - 公共方法
    
    /// 获取所有漏光预设
    func getAllLightLeakPresets() -> [LightLeakPreset] {
        return LightLeakPreset.allPresets
    }
    
    /// 应用漏光效果到图像 - 使用Metal实现
    /// - Parameters:
    ///   - image: 原始图像
    ///   - parameters: 漏光参数
    /// - Returns: 处理后的图像，如果处理失败则返回原图
    func applyLightLeak(to image: UIImage, with parameters: LightLeakParameters) -> UIImage {
        // 如果漏光效果未启用，直接返回原图
        guard parameters.isEnabled else { return image }

        // 确保有选择的预设且参数有效
        guard let preset = parameters.selectedPreset,
              let leakImage = getLeakImage(for: preset.id),
              let metalEngine = self.metalEngine else {
            return image
        }

        // 使用Metal引擎处理漏光效果
        do {
            return try metalEngine.applyLightLeak(to: image, leakImage: leakImage, parameters: parameters)
        } catch {
            print("❌ [LightLeakService] Metal漏光处理失败: \(error)")
            return image
        }
    }
    
    /// 获取预设漏光图像
    /// - Parameter presetId: 预设ID
    /// - Returns: 漏光图像
    private func getLeakImage(for presetId: String) -> UIImage? {
        // 检查缓存
        if let cachedImage = presetCache[presetId] {
            return cachedImage
        }
        
        // 实际开发中，从Assets或文件中加载图像
        // let image = UIImage(named: presetId)
        
        // 模拟图像创建（临时代码，实际开发应替换）
        let tempImage = createPlaceholderLeakImage(for: presetId)
        
        // 存入缓存
        if let tempImage = tempImage {
            presetCache[presetId] = tempImage
        }
        
        return tempImage
    }
    
    /// 创建临时占位漏光图像（仅用于开发）
    private func createPlaceholderLeakImage(for presetId: String) -> UIImage? {
        // 创建不同颜色的示例漏光图像
        let size = CGSize(width: 1024, height: 1024)
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            // 根据ID选择不同颜色
            let color: UIColor
            
            // 根据预设ID选择颜色
            switch presetId {
            case "light_leak_1":
                color = UIColor(red: 1.0, green: 0.8, blue: 0.4, alpha: 0.7) // 暖黄色
            case "light_leak_2":
                color = UIColor(red: 0.4, green: 0.6, blue: 1.0, alpha: 0.7) // 蓝色
            case "light_leak_3":
                color = UIColor(red: 1.0, green: 0.4, blue: 0.4, alpha: 0.7) // 红色
            case "light_leak_4":
                color = UIColor(red: 0.7, green: 0.4, blue: 1.0, alpha: 0.7) // 紫色
            case "light_leak_5":
                color = UIColor(red: 0.4, green: 0.9, blue: 0.6, alpha: 0.7) // 绿色
            default:
                color = UIColor(red: 1.0, green: 1.0, blue: 0.8, alpha: 0.7) // 默认
            }
            
            // 绘制渐变
            let gradient = CGGradient(
                colorsSpace: CGColorSpaceCreateDeviceRGB(),
                colors: [color.cgColor, UIColor.clear.cgColor] as CFArray,
                locations: [0.0, 1.0]
            )!
            
            // 渐变起点和终点
            let center = CGPoint(x: size.width / 2, y: size.height / 2)
            context.cgContext.drawRadialGradient(
                gradient,
                startCenter: center, 
                startRadius: 0,
                endCenter: center, 
                endRadius: size.width / 1.5,
                options: [.drawsBeforeStartLocation, .drawsAfterEndLocation]
            )
        }
    }
} 