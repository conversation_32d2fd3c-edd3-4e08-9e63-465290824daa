import Foundation
import UIKit
import Metal

/// 颗粒效果服务 - 使用Metal实现
class GrainService {
    // 单例模式
    static let shared = GrainService()

    // Metal特殊效果引擎
    private lazy var metalEngine: MetalSpecialEffectsEngine? = {
        do {
            return try MetalSpecialEffectsEngine()
        } catch {
            print("❌ [GrainService] Metal引擎初始化失败: \(error)")
            return nil
        }
    }()

    private init() {
        print("🎨 [GrainService] 初始化 - 使用Metal实现")
    }
    
    /// 获取所有颗粒预设
    func getAllGrainPresets() -> [GrainPreset] {
        // Moved the preset definitions here from GrainModel
        return [
            GrainPreset(id: "fine", name: "细腻", 
                size: 0.3, density: 0.8, contrast: 0.4),
            GrainPreset(id: "standard", name: "标准", 
                size: 0.5, density: 0.6, contrast: 0.5),
            GrainPreset(id: "rough", name: "粗糙", 
                size: 0.8, density: 0.4, contrast: 0.7),
            GrainPreset(id: "film", name: "胶片", 
                size: 0.6, density: 0.7, contrast: 0.6),
            GrainPreset(id: "cinema", name: "电影", 
                size: 0.7, density: 0.5, contrast: 0.65),
            GrainPreset(id: "polaroid", name: "宝丽来", 
                size: 0.65, density: 0.55, contrast: 0.75)
        ]
    }
    
    /// 应用颗粒效果 - 使用Metal实现
    /// - Parameters:
    ///   - image: 原始图像
    ///   - parameters: 颗粒参数
    /// - Returns: 处理后的图像，如果处理失败则返回原图
    func applyGrain(to image: UIImage, with parameters: GrainParameters) -> UIImage {
        guard parameters.isEnabled,
              let metalEngine = self.metalEngine else {
            return image
        }

        // 使用Metal引擎处理颗粒效果
        do {
            return try metalEngine.applyGrain(to: image, parameters: parameters)
        } catch {
            print("❌ [GrainService] Metal颗粒处理失败: \(error)")
            return image
        }
    }
} 