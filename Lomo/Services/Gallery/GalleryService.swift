import Foundation
import UIKit
import Photos
import PhotosUI
import Combine

/// 相册服务 - MVVM-S架构
class GalleryService {

    // MARK: - 单例
    static let shared = GalleryService()

    // MARK: - 私有属性

    /// 图像缓存管理器
    private let imageManager = PHImageManager.default()

    /// 图像请求选项
    private let requestOptions: PHImageRequestOptions = {
        let options = PHImageRequestOptions()
        options.deliveryMode = .highQualityFormat
        options.isNetworkAccessAllowed = true
        options.isSynchronous = false
        return options
    }()

    // MARK: - 初始化
    private init() {}

    // MARK: - 相册权限与加载

    /// 检查相册权限并加载相册分类
    func checkPhotoLibraryPermission() {
        let status = PHPhotoLibrary.authorizationStatus()

        if status == .authorized {
            loadAlbumCategories()
        } else if status == .notDetermined {
            PHPhotoLibrary.requestAuthorization { [weak self] newStatus in
                if newStatus == .authorized {
                    DispatchQueue.main.async {
                        self?.loadAlbumCategories()
                    }
                }
            }
        }
    }

    /// 加载系统相册分类
    func loadAlbumCategories() -> [AlbumCategory] {
        var categories: [AlbumCategory] = []

        // 1. 相机胶卷(所有照片)
        let allPhotosOptions = PHFetchOptions()
        allPhotosOptions.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
        let allPhotos = PHAsset.fetchAssets(with: allPhotosOptions)
        if allPhotos.count > 0 {
            let album = AlbumCategory(
                title: "相机胶卷",
                count: allPhotos.count
            )
            categories.append(album)
        }

        // 2. 系统相册
        let albumOptions = PHFetchOptions()
        let userAlbums = PHAssetCollection.fetchAssetCollections(
            with: .album,
            subtype: .any,
            options: albumOptions
        )

        userAlbums.enumerateObjects { (collection, _, _) in
            let assets = PHAsset.fetchAssets(in: collection, options: nil)
            if assets.count > 0 {
                categories.append(AlbumCategory(
                    title: collection.localizedTitle ?? "未命名相册",
                    count: assets.count
                ))
            }
        }

        // 3. 系统智能相册
        let smartAlbums = PHAssetCollection.fetchAssetCollections(
            with: .smartAlbum,
            subtype: .any,
            options: nil
        )

        smartAlbums.enumerateObjects { (collection, _, _) in
            // 排除一些不需要的智能相册
            if collection.assetCollectionSubtype != .smartAlbumAllHidden &&
               collection.assetCollectionSubtype != .smartAlbumRecentlyAdded {
                let assets = PHAsset.fetchAssets(in: collection, options: nil)
                if assets.count > 0 {
                    categories.append(AlbumCategory(
                        title: collection.localizedTitle ?? "未命名智能相册",
                        count: assets.count
                    ))
                }
            }
        }

        return categories
    }

    /// 从指定相册加载照片
    /// - Parameters:
    ///   - albumTitle: 相册标题
    ///   - isUserAlbum: 是否为用户相册
    func loadPhotosFromAlbum(_ albumTitle: String, isUserAlbum: Bool) -> [PHAsset] {
        var photoAssets: [PHAsset] = []

        if albumTitle == "相机胶卷" {
            // 加载所有照片
            let options = PHFetchOptions()
            options.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
            let allPhotos = PHAsset.fetchAssets(with: options)

            var assets: [PHAsset] = []
            allPhotos.enumerateObjects { (asset, _, _) in
                assets.append(asset)
            }
            return assets
        }

        // 查找指定相册
        let albumType: PHAssetCollectionType = isUserAlbum ? .album : .smartAlbum
        let collections = PHAssetCollection.fetchAssetCollections(
            with: albumType,
            subtype: .any,
            options: nil
        )

        var targetCollection: PHAssetCollection?
        collections.enumerateObjects { (collection, _, stop) in
            if collection.localizedTitle == albumTitle {
                targetCollection = collection
                stop.pointee = true
            }
        }

        if let collection = targetCollection {
            let options = PHFetchOptions()
            options.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
            let assets = PHAsset.fetchAssets(in: collection, options: options)

            var photoList: [PHAsset] = []
            assets.enumerateObjects { (asset, _, _) in
                photoList.append(asset)
            }
            photoAssets = photoList
        }

        return photoAssets
    }

    /// 获取缩略图
    /// - Parameters:
    ///   - asset: 照片资源
    ///   - size: 图片尺寸
    ///   - completion: 回调闭包，返回获取的图片
    func getThumbnail(for asset: PHAsset, size: CGSize, completion: @escaping (UIImage?) -> Void) {
        imageManager.requestImage(
            for: asset,
            targetSize: size,
            contentMode: .aspectFill,
            options: requestOptions
        ) { image, _ in
            completion(image)
        }
    }

    /// 获取原始图片
    /// - Parameters:
    ///   - asset: 照片资源
    ///   - completion: 回调闭包，返回获取的图片
    func getOriginalImage(for asset: PHAsset, completion: @escaping (UIImage?) -> Void) {
        let options = PHImageRequestOptions()
        options.deliveryMode = .highQualityFormat
        options.isNetworkAccessAllowed = true
        options.isSynchronous = false

        imageManager.requestImage(
            for: asset,
            targetSize: PHImageManagerMaximumSize,
            contentMode: .aspectFit,
            options: options
        ) { image, _ in
            completion(image)
        }
    }

    /// 加载应用拍摄的照片
    func loadAppPhotos() -> [PHAsset] {
        var photoAssets: [PHAsset] = []

        // 创建检索选项，按创建日期降序排序
        let options = PHFetchOptions()
        options.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]

        // 尝试查找应用名称创建的相册(一些应用会自动创建自己的相册)
        var foundAppAlbum = false
        let userAlbums = PHAssetCollection.fetchAssetCollections(
            with: .album,
            subtype: .any,
            options: nil
        )

        userAlbums.enumerateObjects { (collection, _, stop) in
            if collection.localizedTitle == "Lomo" {
                foundAppAlbum = true
                let assets = PHAsset.fetchAssets(in: collection, options: options)
                var photoList: [PHAsset] = []
                assets.enumerateObjects { (asset, _, _) in
                    photoList.append(asset)
                }
                photoAssets = photoList
                stop.pointee = true
            }
        }

        // 如果没有找到应用相册，则提示创建空白状态或显示默认内容
        if !foundAppAlbum {
            // 因为无法直接通过sourceApplicationIdentifier识别应用拍摄的照片，我们只依赖Lomo相册
            print("未找到Lomo相册，没有应用拍摄的照片")
        }

        return photoAssets
    }

    /// 删除选中的照片
    func deleteSelectedAssets(_ selectedAssets: [PHAsset], completion: @escaping (Bool) -> Void) {
        guard !selectedAssets.isEmpty else {
            completion(false)
            return
        }

        PHPhotoLibrary.shared().performChanges {
            PHAssetChangeRequest.deleteAssets(selectedAssets as NSArray)
        } completionHandler: { success, error in
            DispatchQueue.main.async {
                completion(success)
            }
        }
    }
}
