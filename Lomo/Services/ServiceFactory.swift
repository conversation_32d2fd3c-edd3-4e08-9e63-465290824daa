import Foundation
import AVFoundation

/// 服务工厂类，负责创建和管理所有服务实例
class ServiceFactory {
    /// 是否运行在模拟器环境中
    static var isSimulator: Bool {
        #if targetEnvironment(simulator)
        return true
        #else
        return false
        #endif
    }
    
    /// 创建所有服务实例
    /// - Returns: 包含所有服务实例的元组
    static func createServices() -> (
        cameraService: CameraServiceProtocol,
        exposureService: ExposureServiceProtocol,
        zoomService: ZoomServiceProtocol,
        uiControlService: UIControlServiceProtocol,
        recordingService: RecordingServiceProtocol
    ) {
        // 使用SessionManager提供的session
        let session = SessionManager.shared.session

        // 创建各个服务 - 直接使用session，不再依赖Controllers
        let cameraService = CameraService(session: session)
        let exposureService = ExposureService(session: session)
        let uiControlService = UIControlService()
        let zoomService = ZoomService(session: session, uiControlService: uiControlService)
        let recordingService = RecordingService(session: session)

        // 注意：CameraViewModel会在初始化时调用uiControlService.setViewModel方法设置自身为ViewModel

        return (
            cameraService: cameraService,
            exposureService: exposureService,
            zoomService: zoomService,
            uiControlService: uiControlService,
            recordingService: recordingService
        )
    }
    
    /// 创建相机预览服务
    /// - Returns: 相机预览服务实例
    static func createPreviewService() -> CameraPreviewServiceProtocol {
        let session = SessionManager.shared.session
        
        if isSimulator {
            // 在模拟器环境中使用模拟预览服务
            return MockCameraPreviewService()
        } else {
            // 在真机环境中使用真实预览服务
            return CameraPreviewService(session: session)
        }
    }
    
    /// 创建滤镜服务
    /// - Returns: 滤镜服务实例
    static func createFilterService() -> FilterServiceProtocol {
        return GalleryFilterService()
    }
} 