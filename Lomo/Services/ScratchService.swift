import Foundation
import UIKit
import Metal

/// 划痕效果服务 - 使用Metal实现
class ScratchService {
    // 单例模式
    static let shared = ScratchService()

    // Metal特殊效果引擎
    private lazy var metalEngine: MetalSpecialEffectsEngine? = {
        do {
            return try MetalSpecialEffectsEngine()
        } catch {
            print("❌ [ScratchService] Metal引擎初始化失败: \(error)")
            return nil
        }
    }()

    private init() {
        print("🎨 [ScratchService] 初始化 - 使用Metal实现")
    }
    
    /// 获取所有划痕预设
    func getAllScratchPresets() -> [ScratchPreset] {
        return ScratchPreset.allPresets
    }
    
    /// 应用划痕效果 - 使用Metal实现
    /// - Parameters:
    ///   - image: 原始图像
    ///   - parameters: 划痕参数
    /// - Returns: 处理后的图像，如果处理失败则返回原图
    func applyScratch(to image: UIImage, with parameters: ScratchParameters) -> UIImage {
        guard parameters.isEnabled,
              let metalEngine = self.metalEngine else {
            return image
        }

        // 使用Metal引擎处理划痕效果
        do {
            return try metalEngine.applyScratch(to: image, parameters: parameters)
        } catch {
            print("❌ [ScratchService] Metal划痕处理失败: \(error)")
            return image
        }
    }

} 