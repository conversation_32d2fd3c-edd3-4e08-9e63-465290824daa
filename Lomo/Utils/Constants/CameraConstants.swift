import Foundation
import UIKit
import SwiftUI

// MARK: - 动画相关常量
enum AnimationConstants {
    // 标准动画时长
    static let duration: Double = 0.5
    // 快速动画时长
    static let quickDuration: Double = 0.4
    // 动画阻尼系数
    static let dampingFraction: Double = 0.75
    // 快速动画阻尼系数
    static let quickDampingFraction: Double = 0.75
    
    // 符号弹性动画
    static let symbolBounceResponse: Double = 0.25      // 动画时长
    static let symbolBounceDamping: Double = 0.75      // 弹性阻尼
    static let symbolBounceScale: Double = 0.8         // 缩放比例
    
    // 标准弹簧动画
    static let standardSpring: Animation = .spring(response: 0.5, dampingFraction: 0.75)
    
    // 标准渐变动画
    static let standardEaseInOut: Animation = .easeInOut(duration: 0.25)
}

// MARK: - UI 相关常量
enum UIConstants {
    // 基础尺寸
    static let lensButtonBaseSize: CGFloat = 0.0425 // 4.25% 屏幕高度
    static let settingsRowVerticalPadding: CGFloat = 0.005  // 0.5% 屏幕高度，设置选项垂直内边距
    static let settingsFirstRowTopPadding: CGFloat = 0.0075  // 0.75% 屏幕高度，第一个选项顶部内边距
    static let settingsLastRowBottomPadding: CGFloat = 0.0075  // 0.75% 屏幕高度，最后一个选项底部内边距
    static let sideButtonSize: CGFloat = 0.04 // 4% 屏幕高度
    static let parameterHeight: CGFloat = 0.032 // 3.2% 屏幕高度
    static let functionIconSize: CGFloat = 0.025 // 2.5% 屏幕高度，功能按钮图标大小
    static let functionButtonWidth: CGFloat = 0.04 // 4% 屏幕高度，3x3功能按钮宽度
    static let headerIconSize: CGFloat = 0.02 // 2% 屏幕高度顶部按钮图标大小
    static let flipIconSize: CGFloat = 0.025 // 2.5% 屏幕高度，翻转按钮图标大小
    static let peakFocusIconSize: CGFloat = 0.03 // 3% 屏幕高度，峰值对焦图标大小
    static let modeSwitchIconSize: CGFloat = 0.0225 // 2.25% 屏幕高度，拍摄模式切换按钮图标大小
    static let tabFontSize: CGFloat = 0.02 // 2% 屏幕高度，标签页文字大小
    
    // 滤镜预览图尺寸
    static let filterPreviewHeight: CGFloat = 0.10 // 10% 屏幕高度，滤镜预览图高度
    static let filterPreviewWidth: CGFloat = 0.6 // 60% 屏幕宽度，滤镜预览图宽度
    
    // 时间间隔
    static let buttonAutoHideInterval: TimeInterval = 8 // 8秒自动收回按钮
    static let dialAutoHideDuration: TimeInterval = 2.0 // 2秒自动隐藏刻度盘
    static let cameraRotationResetDelay: TimeInterval = 0.5 // 0.5秒重置相机旋转状态
    
    // 通用刻度盘样式
    static let dialTickWidth: CGFloat = 1         // 刻度线宽度（主副刻度线统一）
    static let dialMainTickHeight: CGFloat = 10      // 主刻度线高度
    static let dialSubTickHeight: CGFloat = 5        // 副刻度线高度
    static let dialSubTickOpacity: Double = 0.25     // 副刻度线透明度
    static let dialFocalLengthOpacity: Double = 0.25 // 焦距值默认透明度
    static let dialIndicatorColor: Color = Color(uiColor: .systemYellow)   // 指示器和高亮焦距值的颜色
    // 自定义黄色，用于Logo选中背景
    static let logoSelectedColor: Color = Color(red: 255/255, green: 224/255, blue: 98/255) // #ffe062
    static let dialIndicatorSize: CGFloat = 8        // 指示器大小
    static let dialTextSize: CGFloat = 0.015         // 1.5% 屏幕高度
    static let dialTextWeight: Font.Weight = .regular // 文本字重
    static let dialTextOffset: CGFloat = 20          // 文本偏移量
    static let dialBackgroundOpacity: Double = 0.5   // 背景透明度
    
    // 刻度盘尺寸
    static let zoomDialHeight: CGFloat = 0.55        // 55% 屏幕宽度
    static let dialMainTickLength: CGFloat = 0.02    // 2% 屏幕高度，主刻度线长度
    static let dialSubTickLength: CGFloat = 0.01    // 1% 屏幕高度，副刻度线长度
    static let dialTickOffset: CGFloat = 0.01        // 1% 屏幕高度，刻度线偏移量（从圆弧边缘到刻度线起始位置的距离）
    static let dialTextSpacing: CGFloat = 0.02      // 2% 屏幕高度，文本与刻度线的间距
    static let dialMaskHeight: CGFloat = 0.03       // 3% 屏幕高度，遮罩高度
    
    // 镜头容器
    static let lensContainerHeight: CGFloat = 0.05 // 5% 屏幕高度
    static let lensContainerHorizontalPadding: CGFloat = 0.01 // 1% 屏幕宽度
    static let lensButtonSpacing: CGFloat = 0.0075 // 0.75% 屏幕宽度
    
    // 参数面板
    static let parameterExpandedWidth: CGFloat = 0.12 // 12% 屏幕宽度
    static let parameterBackgroundOpacity: Double = 0.5 // 50% 透明度
    
    // 背景透明度
    static let buttonBackgroundOpacity: Double = 0.25 // 25% 透明度
    static let topBarBackgroundOpacity: Double = 0.25 // 25% 透明度
    static let topBarPanelBackgroundOpacity: Double = 0.25 // 25% 透明度
    
    // 新增透明度常量
    static let lightOpacity: Double = 0.25 // 25% 透明度 - 用于轻微背景、描边等
    static let mediumOpacity: Double = 0.5 // 50% 透明度 - 用于中等强度背景
    static let highOpacity: Double = 0.5 // 50% 透明度 - 用于高亮显示和重点突出
    static let effectOpacity: Double = 0.75 // 75% 透明度 - 用于特效和渐变
    
    // 颜色
    static let recordingColor: Color = .red // 录制状态的颜色
    
    // 顶部控制栏
    static let topBarHeight: CGFloat = 0.04 // 4% 屏幕高度
    static let topBarHeightLandscape: CGFloat = 0.05 // 5% 屏幕高度，横屏模式下
    static let topBarIconSize: CGFloat = 0.02 // 2% 屏幕高度
    static let navBarVerticalPadding: CGFloat = 0.01 // 1% 屏幕高度，导航栏垂直内边距
    static let navBarTopPadding: CGFloat = 0.0025 // 0.25% 屏幕高度，导航栏顶部内边距
    static let navBarBottomPadding: CGFloat = 0.005 // 0.5% 屏幕高度导航栏底部内边距
    
    // 底部控制区
    static let bottomControlSpacing: CGFloat = 0.025 // 2.5% 屏幕高度
    static let previewSize: CGFloat = 0.05 // 5% 屏幕高度
    static let previewCornerRadius: CGFloat = 0.01 // 1% 屏幕高度
    static let previewBorderWidth: CGFloat = 0.002 // 0.2% 屏幕高度
    static let shutterButtonSize: CGFloat = 0.075          // 外圈直径（屏幕高度的7.5%）
    static let shutterButtonBorderWidth: CGFloat = 0.005   // 描边宽度（屏幕高度的0.5%）
    static let shutterButtonInnerSize: CGFloat = 0.065     // 内圈直径（屏幕高度的6.5%）
    static let shutterButtonRecordingSize: CGFloat = 0.025 // 录制状态下的内部尺寸（屏幕高度的2.5%）
    static let shutterButtonRecordingCornerRadius: CGFloat = 4 // 录制状态下的圆角半径（4像素）
    
    // 内边距
    static let horizontalPadding: CGFloat = 0.03 // 3% 屏幕宽度
    static let bottomPadding: CGFloat = 0.05 // 5% 屏幕高度
    
    // 字体大小
    static let parameterFontSize: CGFloat = 0.015 // 1.5% 屏幕高度，竖屏状态下的选项文本大小
    static let parameterFontSizeLandscape: CGFloat = 0.0125 // 1.25% 屏幕高度，横屏状态下的选项文本大小
    static let pausedLabelFontSize: CGFloat = 0.0125 // 1.25% 屏幕高度，暂停标签的文本大小
    static let valueTextSize: CGFloat = 0.01 // 1% 屏幕高度功能按钮数值文本大小
    static let verticalSpacing: CGFloat = 0.025 // 2.5% 屏幕高度，通用垂直间距
    static let standardFontSize: CGFloat = 0.0175 // 1.75% 屏幕高度，标准字体大小
    
    // 通用样式
    static let commonCornerRadius: CGFloat = 12 // 通用圆角大小，用于信息显示、选项按钮等组件
    static let freeTrialButtonCornerRadius: CGFloat = 20 // 免费试用按钮的圆角大小
    static let optionButtonCornerRadius: CGFloat = 4 // 选项按钮专用圆角大小
    static let largeCornerRadius: CGFloat = 8 // 大圆角尺寸，用于主要UI元素
    static let smallCornerRadius: CGFloat = 4 // 小圆角尺寸，用于次要UI元素
    
    // 视频模式信息栏
    static let videoInfoHeight: CGFloat = 0.032 // 3.2% 屏幕高度，与参数栏一致
    static let videoInfoSpacing: CGFloat = 0.025 // 2.5% 屏幕高度，与顶部参数栏的间距
    static let videoInfoTopSpacing: CGFloat = 0.0125 // 1.25% 屏幕高度，与顶部参数栏的间距
    static let videoInfoCornerRadius: CGFloat = UIScreen.main.bounds.height * 0.004 // 0.4% 屏幕高度，视频信息显示区域的圆角大小
    static let recordingTimeHeight: CGFloat = 0.025 // 2.5% 屏幕高度，录制时间显示高度
    static let audioMeterWidth: CGFloat = 0.02 // 2% 屏幕宽度，单个音频条宽度
    static let audioMeterHeight: CGFloat = 0.003 // 0.3% 屏幕高度，单个音频条高度
    static let audioMeterSpacing: CGFloat = 0.01 // 1% 屏幕宽度，声道之间的间距
    static let audioMeterChannelSpacing: CGFloat = 0.001 // 0.1% 屏幕高度，双声道之间的间距
    static let audioMeterCount: Int = 20 // 音频监视器的条数
    
    // 音频波形相关
    static let waveformHeight: CGFloat = 0.07875 // 7.875% 屏幕高度，音频波形高度（包含标签和间距）
    static let waveformWidth: CGFloat = 0.0575 // 5.75% 屏幕宽度，音频波形宽度
    
    // 焦距显示通用样式
    static let zoomDisplayFontSize: CGFloat = 0.0175        // 1.75% 屏幕高度，倍率数字大小
    static let zoomDisplayMultiplierSize: CGFloat = 0.0125  // 1.25% 屏幕高度，乘号大小
    static let zoomDisplaySpacing: CGFloat = 0              // 倍率数字与乘号之间的间距
    static let zoomDisplayWeight: Font.Weight = .semibold   // 倍率数字字重
    static let zoomDisplayMultiplierWeight: Font.Weight = .heavy  // 乘号字重
    static let zoomDisplayVerticalSpacing: CGFloat = 2      // 倍率与焦距值之间的垂直间距
    
    // 色温显示通用样式
    static let temperatureDisplayFontSize: CGFloat = 0.015        // 1.5% 屏幕高度，色温数字大小
    static let temperatureDisplayWeight: Font.Weight = .semibold   // 色温数字字重
    static let temperatureDisplayVerticalSpacing: CGFloat = 2      // 色温值之间的垂直间距
    
    // 色调显示通用样式
    static let tintDisplayFontSize: CGFloat = 0.0175              // 1.75% 屏幕高度，色调数字大小
    static let tintDisplayWeight: Font.Weight = .semibold        // 色调数字字重
    static let tintDisplayVerticalSpacing: CGFloat = 2           // 色调值之间的垂直间距
    static let tintDialMainTickLength: CGFloat = 0.02           // 2% 屏幕高度，主刻度线长度
    static let tintDialSubTickLength: CGFloat = 0.01            // 1% 屏幕高度，副刻度线长度
    static let tintDialTickOffset: CGFloat = 0.01               // 1% 屏幕高度，刻度线偏移量
    static let tintDialSubTickOpacity: Double = 0.25            // 副刻度线透明度
    
    // ISO显示通用样式
    static let isoDisplayFontSize: CGFloat = 0.0175              // 1.75% 屏幕高度，ISO数字大小
    static let isoDisplayWeight: Font.Weight = .semibold        // ISO数字字重
    static let isoDisplayVerticalSpacing: CGFloat = 2           // ISO值之间的垂直间距
    static let isoDialMainTickLength: CGFloat = 0.02           // 2% 屏幕高度，主刻度线长度
    static let isoDialSubTickLength: CGFloat = 0.01            // 1% 屏幕高度，副刻度线长度
    static let isoDialTickOffset: CGFloat = 0.01               // 1% 屏幕高度，刻度线偏移量
    static let isoDialSubTickOpacity: Double = 0.25            // 副刻度线透明度
    
    // 快门显示通用样式
    static let shutterDisplayFontSize: CGFloat = 0.015              // 1.5% 屏幕高度，快门值数字大小
    static let shutterDisplayWeight: Font.Weight = .semibold        // 快门值数字字重
    static let shutterDisplayVerticalSpacing: CGFloat = 2           // 快门值之间的垂直间距
    
    // 焦点显示通用样式
    static let focusDisplayFontSize: CGFloat = standardFontSize              // 1.75% 屏幕高度，焦点值数字大小
    static let focusDisplayWeight: Font.Weight = .semibold        // 焦点值数字字重
    static let focusDisplayVerticalSpacing: CGFloat = 2           // 焦点值之间的垂直间距
    static let focusDialMainTickLength: CGFloat = 0.02           // 2% 屏幕高度，主刻度线长度
    static let focusDialSubTickLength: CGFloat = 0.01            // 1% 屏幕高度，副刻度线长度
    static let focusDialTickOffset: CGFloat = 0.01               // 1% 屏幕高度，刻度线偏移量
    static let focusDialSubTickOpacity: Double = 0.25            // 副刻度线透明度
    
    // 刻度盘统一边缘距离（到圆弧外边缘的距离）
    static let dialEdgeDistance: CGFloat = 0.06  // 6%屏幕高度
    
    // 按钮组件与3x3按钮展开后的间距（1%屏幕高度）
    static let buttonGroupSpacing: CGFloat = 0.01
    
    // 选项组件相关
    static let optionButtonHeight: CGFloat = 0.032  // 3.2%屏幕高度，选项按钮高度
    static let optionButtonSpacing: CGFloat = 0.02 // 2%屏幕宽度，选项按钮间距
    static let optionButtonPadding: CGFloat = 0.02 // 2%屏幕宽度，选项按钮水平内边距

    // 电影胶片风格水印元素间距
    static let filmStyleElementSpacing: CGFloat = 0.005 // 0.5% 屏幕高度，电影胶片风格水印中元素间距

    // 电影胶片风格水印底部Y轴偏移量乘数 (相对于容器高度)
    static let filmStyleBottomOffsetYMultiplier: CGFloat = 0.08 // 8% 容器高度

    // 水印预览图元素常量
    static let watermarkCircleSize: CGFloat = 0.004 // 圆形直径为屏幕高度的0.4%
    static let watermarkRectWidth: CGFloat = 0.016 // 矩形宽度为屏幕高度的1.6%
    static let watermarkRectHeight: CGFloat = 0.002 // 矩形高度为屏幕高度的0.2%
    static let watermarkElementSpacing: CGFloat = 0.002 // 元素间距为屏幕高度的0.2%，与矩形高度相等
    static let watermarkBorderNormalWidth: CGFloat = 0.004 // 普通边框宽度为屏幕高度的0.4%
    static let watermarkBorder2Width: CGFloat = 0.016 // 水印2底部边框宽度为屏幕高度的1.6%
    static let watermarkBorder4Width: CGFloat = 0.012 // 水印4底部边框宽度为屏幕高度的1.2%
    static let watermarkBorder5LeftWidth: CGFloat = 0.024 // 水印5左侧边框宽度为屏幕高度的2.4%
    static let watermarkPreviewDecorColor: Color = Color(uiColor: .systemGray5) // 水印装饰元素颜色
    static let watermarkFilmDecorColor: Color = .white // 胶片风格水印装饰元素颜色
}

// MARK: - 相机参数默认值
enum CameraDefaults {
    // 视频参数
    static let videoCodec = "HEVC"
    static let videoColorSpace = "SDR"
    static let videoResolution = "4K"
    static let videoFrameRate = "30fps"
    static let videoAspectRatio = "16:9"
    
    // 照片参数
    static let photoFormat = "JPG"
    static let photoMode = "timer"
    static let photoAspectRatio = "4:3"
    
    // 镜头
    static let defaultLens = "1"
}

// MARK: - 相机参数阈值常量
enum CameraThresholds {
    // A/M模式切换阈值
    static let tintThreshold: Double = 0.5           // 色调阈值
    static let temperatureBaseValue: Double = 5500.0  // 色温基准值
    static let temperatureThreshold: Double = 0.5     // 色温阈值
    static let exposureThreshold: Double = 0.1        // 曝光阈值
    static let isoBaseValue: Double = 100.0           // ISO基准值
    static let isoThreshold: Double = 0.5             // ISO阈值
    static let shutterThreshold: Double = 0.5         // 快门阈值
    static let focusBaseValue: Double = 0.5           // 对焦基准值
    static let focusThreshold: Double = 0.01          // 对焦阈值
}
