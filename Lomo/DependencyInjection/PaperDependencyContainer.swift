import Foundation
import SwiftData

/// 相纸模块依赖注入容器 - MVVM-S架构
/// 管理相纸模块的所有依赖关系，确保单一职责和可测试性
class PaperDependencyContainer {

    // MARK: - 单例
    static let shared = PaperDependencyContainer()

    // MARK: - 依赖实例
    private var _paperService: PaperService?
    private var _modelContainer: ModelContainer?

    // MARK: - 初始化
    private init() {
        print("🎨 [PaperDependencyContainer] 初始化完成")
    }

    // MARK: - 公共接口

    /// 获取相纸服务
    var paperService: PaperService {
        if let service = _paperService {
            return service
        }

        let service = PaperService()
        _paperService = service
        return service
    }

    /// 获取模型容器
    var modelContainer: ModelContainer {
        if let container = _modelContainer {
            return container
        }

        // 使用SharedService的模型容器
        let container = SharedService.shared.container
        _modelContainer = container
        return container
    }

    // MARK: - ViewModel工厂方法

    /// 创建相纸ViewModel
    /// - Returns: 配置好依赖的PaperViewModel实例
    func createPaperViewModel() -> PaperViewModel {
        let viewModel = PaperViewModel(paperService: paperService)
        print("🎨 [PaperDependencyContainer] 创建PaperViewModel")
        return viewModel
    }

    /// 创建相纸View
    /// - Returns: 配置好依赖的PaperView实例
    func createPaperView() -> PaperView {
        let viewModel = createPaperViewModel()
        let view = PaperView(paperViewModel: viewModel)
        print("🎨 [PaperDependencyContainer] 创建PaperView")
        return view
    }

    // MARK: - 生命周期管理

    /// 预热依赖（提前初始化）
    func warmUp() {
        print("🔥 [PaperDependencyContainer] 开始预热依赖...")

        // 预初始化核心服务
        _ = paperService

        print("🔥 [PaperDependencyContainer] 依赖预热完成")
    }

    /// 清理资源
    func cleanup() {
        _paperService = nil
        // 注意：不清理modelContainer，因为它是共享的

        print("🧹 [PaperDependencyContainer] 资源清理完成")
    }
}

// MARK: - 扩展：便捷访问方法

extension PaperDependencyContainer {

    /// 便捷方法：直接获取配置好的PaperViewModel
    static func paperViewModel() -> PaperViewModel {
        return shared.createPaperViewModel()
    }

    /// 便捷方法：直接获取配置好的PaperView
    static func paperView() -> PaperView {
        return shared.createPaperView()
    }
}
