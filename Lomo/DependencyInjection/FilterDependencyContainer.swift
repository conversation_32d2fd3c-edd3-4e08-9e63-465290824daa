import Foundation
import SwiftData

/// 滤镜应用模块的依赖注入容器 - MVVM-S架构
/// 管理滤镜模块的所有依赖关系，确保单一职责和可测试性
class FilterDependencyContainer {

    // MARK: - 单例
    static let shared = FilterDependencyContainer()

    // MARK: - 依赖实例
    private var _filterService: FilterService?
    private var _modelContainer: ModelContainer?

    // MARK: - 初始化
    private init() {
        print("🎨 [FilterDependencyContainer] 初始化完成")
    }

    // MARK: - 公共接口

    /// 获取滤镜服务
    var filterService: FilterService {
        if let service = _filterService {
            return service
        }

        let service = FilterService()
        _filterService = service
        return service
    }

    /// 获取模型容器
    var modelContainer: ModelContainer {
        if let container = _modelContainer {
            return container
        }

        // 使用SharedService的模型容器
        let container = SharedService.shared.container
        _modelContainer = container
        return container
    }

    // MARK: - ViewModel工厂方法

    /// 创建滤镜ViewModel
    /// - Returns: 配置好依赖的FilterViewModel实例
    func createFilterViewModel() -> FilterViewModel {
        let viewModel = FilterViewModel()
        print("🎨 [FilterDependencyContainer] 创建FilterViewModel")
        return viewModel
    }

    /// 创建滤镜View
    /// - Returns: 配置好依赖的FilterView实例
    func createFilterView() -> FilterView {
        let viewModel = createFilterViewModel()
        let view = FilterView(filterViewModel: viewModel)
        print("🎨 [FilterDependencyContainer] 创建FilterView")
        return view
    }

    // MARK: - 生命周期管理

    /// 预热依赖（提前初始化）
    func warmUp() {
        print("🔥 [FilterDependencyContainer] 开始预热依赖...")

        // 预初始化核心服务
        _ = filterService

        print("🔥 [FilterDependencyContainer] 依赖预热完成")
    }

    /// 清理资源
    func cleanup() {
        _filterService = nil
        // 注意：不清理modelContainer，因为它是共享的

        print("🧹 [FilterDependencyContainer] 资源清理完成")
    }
}

// MARK: - 扩展：便捷访问方法

extension FilterDependencyContainer {

    /// 便捷方法：直接获取配置好的FilterViewModel
    static func filterViewModel() -> FilterViewModel {
        return shared.createFilterViewModel()
    }

    /// 便捷方法：直接获取配置好的FilterView
    static func filterView() -> FilterView {
        return shared.createFilterView()
    }
}