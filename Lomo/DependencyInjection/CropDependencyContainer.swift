import Foundation
import SwiftData

/// 裁切模块依赖注入容器 - MVVM-S架构
/// 管理裁切模块的所有依赖关系，确保单一职责和可测试性
class CropDependencyContainer {

    // MARK: - 单例
    static let shared = CropDependencyContainer()

    // MARK: - 依赖实例
    private var _cropService: CropService?
    private var _modelContainer: ModelContainer?

    // MARK: - 初始化
    private init() {
        print("🎨 [CropDependencyContainer] 初始化完成")
    }

    // MARK: - 公共接口

    /// 获取裁切服务
    var cropService: CropService {
        if let service = _cropService {
            return service
        }

        let service = CropService()
        _cropService = service
        return service
    }

    /// 获取模型容器
    var modelContainer: ModelContainer {
        if let container = _modelContainer {
            return container
        }

        // 使用SharedService的模型容器
        let container = SharedService.shared.container
        _modelContainer = container
        return container
    }

    // MARK: - ViewModel工厂方法

    /// 创建裁切ViewModel
    /// - Returns: 配置好依赖的CropViewModel实例
    func createCropViewModel() -> CropViewModel {
        let viewModel = CropViewModel(cropService: cropService)
        print("🎨 [CropDependencyContainer] 创建CropViewModel")
        return viewModel
    }

    /// 创建裁切View
    /// - Returns: 配置好依赖的CropView实例
    func createCropView() -> CropView {
        let viewModel = createCropViewModel()
        let view = CropView(cropViewModel: viewModel)
        print("🎨 [CropDependencyContainer] 创建CropView")
        return view
    }

    // MARK: - 生命周期管理

    /// 预热依赖（提前初始化）
    func warmUp() {
        print("🔥 [CropDependencyContainer] 开始预热依赖...")

        // 预初始化核心服务
        _ = cropService

        print("🔥 [CropDependencyContainer] 依赖预热完成")
    }

    /// 清理资源
    func cleanup() {
        _cropService = nil
        // 注意：不清理modelContainer，因为它是共享的

        print("🧹 [CropDependencyContainer] 资源清理完成")
    }
}

// MARK: - 扩展：便捷访问方法

extension CropDependencyContainer {

    /// 便捷方法：直接获取配置好的CropViewModel
    static func cropViewModel() -> CropViewModel {
        return shared.createCropViewModel()
    }

    /// 便捷方法：直接获取配置好的CropView
    static func cropView() -> CropView {
        return shared.createCropView()
    }
}
