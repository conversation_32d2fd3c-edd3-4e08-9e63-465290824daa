import Foundation
import SwiftData

/// 特效模块依赖注入容器 - MVVM-S架构
/// 管理特效模块的所有依赖关系，确保单一职责和可测试性
class EffectsDependencyContainer {

    // MARK: - 单例
    static let shared = EffectsDependencyContainer()

    // MARK: - 依赖实例
    private var _effectsService: EffectsService?
    private var _modelContainer: ModelContainer?

    // MARK: - 初始化
    private init() {
        print("🎨 [EffectsDependencyContainer] 初始化完成")
    }

    // MARK: - 公共接口

    /// 获取特效服务
    var effectsService: EffectsService {
        if let service = _effectsService {
            return service
        }

        let service = EffectsService.shared
        _effectsService = service
        return service
    }

    /// 获取模型容器
    var modelContainer: ModelContainer {
        if let container = _modelContainer {
            return container
        }

        // 使用SharedService的模型容器
        let container = SharedService.shared.container
        _modelContainer = container
        return container
    }

    // MARK: - ViewModel工厂方法

    /// 创建特效ViewModel
    /// - Returns: 配置好依赖的EffectsViewModel实例
    func createEffectsViewModel() -> EffectsViewModel {
        let viewModel = EffectsViewModel(effectsService: effectsService)
        print("🎨 [EffectsDependencyContainer] 创建EffectsViewModel")
        return viewModel
    }

    /// 创建特效View
    /// - Returns: 配置好依赖的EffectsView实例
    func createEffectsView() -> EffectsView {
        let effectsViewModel = createEffectsViewModel()
        let view = EffectsView(effectsViewModel: effectsViewModel)
        print("🎨 [EffectsDependencyContainer] 创建EffectsView")
        return view
    }

    // MARK: - 生命周期管理

    /// 预热依赖（提前初始化）
    func warmUp() {
        print("🔥 [EffectsDependencyContainer] 开始预热依赖...")

        // 预初始化核心服务
        _ = effectsService

        print("🔥 [EffectsDependencyContainer] 依赖预热完成")
    }

    /// 清理资源
    func cleanup() {
        _effectsService = nil
        // 注意：不清理modelContainer，因为它是共享的

        print("🧹 [EffectsDependencyContainer] 资源清理完成")
    }
}

// MARK: - 扩展：便捷访问方法

extension EffectsDependencyContainer {

    /// 便捷方法：直接获取配置好的EffectsViewModel
    static func effectsViewModel() -> EffectsViewModel {
        return shared.createEffectsViewModel()
    }

    /// 便捷方法：直接获取配置好的EffectsView
    static func effectsView() -> EffectsView {
        return shared.createEffectsView()
    }
}
