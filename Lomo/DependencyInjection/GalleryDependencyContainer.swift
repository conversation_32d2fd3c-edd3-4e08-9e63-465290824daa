import Foundation
import SwiftData
import SwiftUI

/// 相册模块依赖注入容器 - MVVM-S架构
/// 管理相册模块的所有依赖关系，确保单一职责和可测试性
class GalleryDependencyContainer {

    // MARK: - 单例
    static let shared = GalleryDependencyContainer()

    // MARK: - 依赖实例
    private var _galleryService: GalleryService?
    private var _modelContainer: ModelContainer?

    // MARK: - 初始化
    private init() {
        print("📸 [GalleryDependencyContainer] 初始化完成")
    }

    // MARK: - 公共接口

    /// 获取相册服务
    var galleryService: GalleryService {
        if let service = _galleryService {
            return service
        }

        let service = GalleryService.shared
        _galleryService = service
        return service
    }

    /// 获取模型容器
    var modelContainer: ModelContainer {
        if let container = _modelContainer {
            return container
        }

        // 使用SharedService的模型容器
        let container = SharedService.shared.container
        _modelContainer = container
        return container
    }

    // MARK: - ViewModel工厂方法

    /// 创建相册ViewModel
    /// - Returns: 配置好依赖的GalleryViewModel实例
    func createGalleryViewModel() -> GalleryViewModel {
        let viewModel = GalleryViewModel(galleryService: galleryService)
        print("📸 [GalleryDependencyContainer] 创建GalleryViewModel")
        return viewModel
    }

    /// 创建相册View
    /// - Parameter sharedTabViewModel: 共享标签视图模型
    /// - Returns: 配置好依赖的GalleryView实例
    func createGalleryView(sharedTabViewModel: SharedTabViewModel) -> GalleryView {
        let galleryViewModel = createGalleryViewModel()
        let watermarkService = WatermarkService()
        let view = GalleryView(viewModel: galleryViewModel, sharedTabViewModel: sharedTabViewModel, watermarkService: watermarkService)
        print("📸 [GalleryDependencyContainer] 创建GalleryView")
        return view
    }

    // MARK: - 生命周期管理

    /// 预热依赖（提前初始化）
    func warmUp() {
        print("🔥 [GalleryDependencyContainer] 开始预热依赖...")

        // 预初始化核心服务
        _ = galleryService

        print("🔥 [GalleryDependencyContainer] 依赖预热完成")
    }

    /// 清理资源
    func cleanup() {
        _galleryService = nil
        // 注意：不清理modelContainer，因为它是共享的

        print("🧹 [GalleryDependencyContainer] 资源清理完成")
    }
}

// MARK: - 扩展：便捷访问方法

extension GalleryDependencyContainer {

    /// 便捷方法：直接获取配置好的GalleryViewModel
    static func galleryViewModel() -> GalleryViewModel {
        return shared.createGalleryViewModel()
    }

    /// 便捷方法：直接获取配置好的GalleryView
    /// - Parameter sharedTabViewModel: 共享标签视图模型
    static func galleryView(sharedTabViewModel: SharedTabViewModel) -> GalleryView {
        return shared.createGalleryView(sharedTabViewModel: sharedTabViewModel)
    }
}
