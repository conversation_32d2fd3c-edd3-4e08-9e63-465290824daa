import Foundation
import SwiftData

/// 调节模块的依赖注入容器 - MVVM-S架构
/// 管理调节模块的所有依赖关系，确保单一职责和可测试性
class AdjustDependencyContainer {

    // MARK: - 单例
    static let shared = AdjustDependencyContainer()

    // MARK: - 依赖实例
    private var _adjustService: AdjustService?
    private var _modelContainer: ModelContainer?

    // MARK: - 初始化
    private init() {
        print("🎨 [AdjustDependencyContainer] 初始化完成")
    }

    // MARK: - 公共接口

    /// 获取调节服务
    var adjustService: AdjustService {
        if let service = _adjustService {
            return service
        }

        let service = AdjustService.shared
        _adjustService = service
        return service
    }

    /// 获取模型容器
    var modelContainer: ModelContainer {
        if let container = _modelContainer {
            return container
        }

        // 使用SharedService的模型容器
        let container = SharedService.shared.container
        _modelContainer = container
        return container
    }

    // MARK: - ViewModel工厂方法

    /// 创建调节ViewModel
    /// - Returns: 配置好依赖的AdjustViewModel实例
    func createAdjustViewModel() -> AdjustViewModel {
        let viewModel = AdjustViewModel()
        print("🎨 [AdjustDependencyContainer] 创建AdjustViewModel")
        return viewModel
    }

    /// 创建调节View
    /// - Returns: 配置好依赖的AdjustView实例
    func createAdjustView() -> AdjustView {
        let viewModel = createAdjustViewModel()
        let view = AdjustView(adjustViewModel: viewModel)
        print("🎨 [AdjustDependencyContainer] 创建AdjustView")
        return view
    }

    // MARK: - 生命周期管理

    /// 预热依赖（提前初始化）
    func warmUp() {
        print("🔥 [AdjustDependencyContainer] 开始预热依赖...")

        // 预初始化核心服务
        _ = adjustService

        print("🔥 [AdjustDependencyContainer] 依赖预热完成")
    }

    /// 清理资源
    func cleanup() {
        _adjustService = nil
        // 注意：不清理modelContainer，因为它是共享的

        print("🧹 [AdjustDependencyContainer] 资源清理完成")
    }
}

// MARK: - 扩展：便捷访问方法

extension AdjustDependencyContainer {

    /// 便捷方法：直接获取配置好的AdjustViewModel
    static func adjustViewModel() -> AdjustViewModel {
        return shared.createAdjustViewModel()
    }

    /// 便捷方法：直接获取配置好的AdjustView
    static func adjustView() -> AdjustView {
        return shared.createAdjustView()
    }
}
