import Foundation
import SwiftUI

// MARK: - 水印模块依赖注入容器
/// 水印模块依赖注入容器 - 渐进式重构的第4步
/// **重要：这是一个测试版本，不会影响现有的Manager系统**
/// **目标：为新的MVVM-S架构提供依赖管理**
class WatermarkDependencyContainer {
    
    // MARK: - 单例实例
    static let shared = WatermarkDependencyContainer()
    
    // MARK: - 私有属性
    private var _watermarkService: WatermarkService?
    private let serviceQueue = DispatchQueue(label: "watermark.dependency.queue", qos: .userInitiated)
    
    // MARK: - 初始化
    private init() {
        print("🏭 [WatermarkDependencyContainer] 初始化依赖容器（渐进式重构 - 第4步）")
    }
    
    // MARK: - 公共接口

    /// 获取水印服务
    /// 使用懒加载和线程安全的单例模式
    var watermarkService: WatermarkService {
        return serviceQueue.sync {
            if let existingService = _watermarkService {
                print("🔄 [WatermarkDependencyContainer] 复用现有WatermarkService实例")
                return existingService
            }

            print("🏭 [WatermarkDependencyContainer] 创建新的WatermarkService实例")
            let service = WatermarkService()
            _watermarkService = service
            return service
        }
    }

    /// 重置WatermarkService实例（用于测试或重新初始化）
    func resetWatermarkService() {
        serviceQueue.sync {
            print("🔄 [WatermarkDependencyContainer] 重置WatermarkService实例")
            _watermarkService = nil
        }
    }
    
    // MARK: - ViewModel层工厂方法
    
    /// 创建WatermarkViewModel实例
    /// 使用依赖注入的方式创建ViewModel
    func makeWatermarkViewModel() -> WatermarkViewModel {
        print("🏭 [WatermarkDependencyContainer] 创建WatermarkViewModel实例")
        return WatermarkViewModel(watermarkService: watermarkService)
    }
    
    // MARK: - View层工厂方法
    
    /// 创建WatermarkView实例
    /// 使用依赖注入的方式创建完整的View层组件
    func makeWatermarkView(
        isKeyboardVisible: Binding<Bool>,
        previewContainer: UIView? = nil
    ) -> WatermarkView {
        print("🏭 [WatermarkDependencyContainer] 创建WatermarkView实例")
        return WatermarkView(
            isKeyboardVisible: isKeyboardVisible,
            previewContainer: previewContainer,
            viewModel: makeWatermarkViewModel()
        )
    }
    
    // MARK: - 依赖生命周期管理
    
    /// 清理所有缓存的依赖实例
    /// 用于内存管理和测试场景
    func cleanup() {
        serviceQueue.sync {
            print("🧹 [WatermarkDependencyContainer] 清理所有依赖实例")
            _watermarkService = nil
        }
    }
    
    /// 验证依赖链完整性
    /// 用于调试和测试
    func validateDependencyChain() -> Bool {
        print("🔍 [WatermarkDependencyContainer] 验证依赖链完整性")

        do {
            // 测试Service创建
            let service = watermarkService
            let settings = service.getSettings()
            print("✅ Service层验证通过，当前水印类型: \(settings.activeWatermarkStyleType)")

            // 测试ViewModel创建
            let viewModel = makeWatermarkViewModel()
            print("✅ ViewModel层验证通过，初始水印类型: \(viewModel.selectedWatermarkType)")

            return true
        } catch {
            print("❌ 依赖链验证失败: \(error)")
            return false
        }
    }
}
