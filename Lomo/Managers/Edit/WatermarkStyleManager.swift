import SwiftUI

// MARK: - 水印样式协议
/// 定义水印样式的基本接口
protocol WatermarkStyle {
    /// 应用水印样式到预览视图
    func apply(to previewContainer: UIView)
    
    /// 移除水印效果
    func remove(from previewContainer: UIView)
}

// MARK: - 水印管理器
/// 负责水印样式的切换和管理
class WatermarkManager {
    /// 当前应用的水印样式
    private var currentStyle: WatermarkStyle?
    
    /// 主预览视图的引用
    private weak var previewContainer: UIView?
    
    /// 初始化水印管理器
    /// - Parameter previewContainer: 预览容器视图
    init(previewContainer: UIView) {
        self.previewContainer = previewContainer
    }
    
    /// 应用选择的水印样式
    /// - Parameter style: 要应用的水印样式，nil表示移除水印
    func applyWatermarkStyle(_ style: WatermarkStyle?) {
        // 移除当前水印
        removeCurrentWatermark()
        
        // 应用新水印
        if let style = style, let previewContainer = previewContainer {
            style.apply(to: previewContainer)
            currentStyle = style
        }
    }
    
    /// 移除当前水印
    func removeCurrentWatermark() {
        if let style = currentStyle, let previewContainer = previewContainer {
            style.remove(from: previewContainer)
            currentStyle = nil
        }
    }

    /// 检查此管理器是否正在管理指定的预览容器
    /// - Parameter container: 要检查的预览容器
    /// - Returns: 如果此管理器当前弱引用相同的容器实例，则为 true
    func isPreviewing(on container: UIView) -> Bool {
        guard let currentContainer = self.previewContainer else {
            return false // 如果自身没有容器，则肯定不是同一个
        }
        return currentContainer === container // 使用实例比较 (===)
    }
}

// MARK: - 水印管理器提供者
/// 用于在不同组件之间共享水印管理器的单例
class WatermarkManagerProvider {
    /// 共享实例
    static let shared = WatermarkManagerProvider()
    
    /// 水印管理器实例
    var watermarkManager: WatermarkManager?
    
    /// 私有初始化方法
    private init() {}
    
    /// 设置水印管理器
    /// - Parameter previewContainer: 预览容器视图
    func setupWatermarkManager(with previewContainer: UIView) {
        if let existingManager = self.watermarkManager, existingManager.isPreviewing(on: previewContainer) {
            // 管理器已存在且针对同一个预览容器，无需重新创建
            // print("ℹ️ WatermarkManagerProvider: Reusing existing WatermarkManager for the same container.")
            return
        }
        // print("ℹ️ WatermarkManagerProvider: Setting up new WatermarkManager for container: \(previewContainer).")
        watermarkManager = WatermarkManager(previewContainer: previewContainer)
    }
} 