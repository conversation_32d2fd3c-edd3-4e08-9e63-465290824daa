import Foundation
import SwiftUI
import Photos
import PhotosUI
import Combine

/// 相册功能的视图模型，处理相册相关的所有状态和操作
class GalleryViewModel: ObservableObject {
    // MARK: - 发布属性
    
    /// 选中的主选项卡
    @Published var selectedMainTab: MainGalleryTab = .myWorks
    
    /// 相册分类列表
    @Published var albumCategories: [AlbumCategory] = []
    
    /// 当前选中的相册
    @Published var selectedAlbum: AlbumCategory?
    
    /// 当前相册中的照片资源
    @Published var photoAssets: [PHAsset] = []
    
    /// 正在加载
    @Published var isLoading: Bool = false
    
    /// 选中的照片索引（用于预览）
    @Published var selectedPhotoIndex: Int?
    
    /// 选中的水印/编辑类别
    @Published var selectedCategory: WatermarkCategory = .watermark
    
    // 选择模式相关状态
    /// 是否处于选择模式
    @Published var isSelectionMode: Bool = false
    
    /// 已选中的照片资源
    @Published var selectedAssets: [PHAsset] = []
    
    /// 是否为拼图水印选择模式（custom23或custom24）
    @Published var isPuzzleWatermarkMode: Bool = false
    
    /// 自定义水印23需要的照片数量
    private let custom23RequiredPhotoCount: Int = 4
    
    /// 自定义水印24需要的照片数量
    private let custom24RequiredPhotoCount: Int = 5
    
    /// 是否显示拼图水印选择提示
    @Published var showPuzzleWatermarkAlert: Bool = false
    
    /// 拼图水印选择提示信息
    @Published var puzzleWatermarkAlertMessage: String = ""
    
    /// 是否为拼图水印选择模式（兼容旧代码）
    @Published var isPuzzleWatermarkSelection: Bool = false
    
    /// 是否显示Custom23选择提示（兼容旧代码）
    @Published var showCustom23SelectionAlert: Bool = false
    
    /// Custom23选择提示信息（兼容旧代码）
    @Published var custom23AlertMessage: String = ""
    
    /// 是否为拼图水印选择模式（兼容旧代码）
    @Published var isCustom23Selection: Bool = false
    
    // 应用名称 - 用于识别应用拍摄的照片
    private let appBundleIdentifier = Bundle.main.bundleIdentifier ?? "com.yourcompany.Lomo"
    
    // MARK: - 依赖注入
    private let galleryService: GalleryService
    private let watermarkService = WatermarkService()

    // MARK: - 初始化方法

    init(galleryService: GalleryService) {
        self.galleryService = galleryService

        // 初始化时检查相册权限
        galleryService.checkPhotoLibraryPermission()

        // 检查当前激活的水印类型是否为拼图水印
        checkIfPuzzleWatermarkActive()

        // 设置通知监听器
        setupNotificationObservers()
    }
    
    // MARK: - 通知与水印类型检查
    
    /// 设置通知监听器
    private func setupNotificationObservers() {
        // 监听水印类型变化的通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleWatermarkTypeChanged),
            name: Notification.Name("WatermarkTypeChanged"),
            object: nil
        )
        
        // 监听拼图水印照片选择提示通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleShowPuzzleWatermarkAlert(_:)),
            name: Notification.Name("ShowPuzzleWatermarkAlert"),
            object: nil
        )
        
        // 监听自定义水印23照片选择提示通知（兼容旧代码）
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleShowPuzzleWatermarkAlert(_:)),
            name: Notification.Name("ShowCustom23Alert"),
            object: nil
        )
        
        // 为了向后兼容，同时监听旧的通知名称
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleShowPuzzleWatermarkAlert(_:)),
            name: Notification.Name("ShowPuzzleWatermarkAlert"),
            object: nil
        )
    }
    
    /// 处理水印类型变化的通知
    @objc private func handleWatermarkTypeChanged(_ notification: Notification) {
        checkIfPuzzleWatermarkActive()
    }
    
    /// 处理显示拼图水印提示的通知
    @objc private func handleShowPuzzleWatermarkAlert(_ notification: Notification) {
        if let message = notification.object as? String {
            puzzleWatermarkAlertMessage = message
            showPuzzleWatermarkAlert = true
            
            // 为了向后兼容，同时设置旧的变量
            custom23AlertMessage = message
            showCustom23SelectionAlert = true
        }
    }
    
    /// 检查当前激活的水印类型是否为拼图水印
    private func checkIfPuzzleWatermarkActive() {
        let watermarkSettings = watermarkService.getSettings()
        let isPuzzle = watermarkSettings.activeWatermarkStyleType == "custom23" || watermarkSettings.activeWatermarkStyleType == "custom24"
        
        // 设置拼图水印选择模式
        isPuzzleWatermarkMode = isPuzzle
        isPuzzleWatermarkSelection = isPuzzle
        
        // 为了向后兼容，同时设置旧的变量
        isCustom23Selection = isPuzzle
    }
    
    // MARK: - 相册权限与加载

    /// 加载相册分类
    func loadAlbumCategories() {
        isLoading = true
        let categories = galleryService.loadAlbumCategories()

        DispatchQueue.main.async { [weak self] in
            self?.albumCategories = categories
            self?.isLoading = false

            // 根据当前选中的标签加载对应的照片
            if let self = self {
                if self.selectedMainTab == .myWorks {
                    self.loadAppPhotos()
                } else if self.selectedAlbum == nil && categories.count > 0 {
                    // 默认选中第一个相册
                    self.selectedAlbum = categories[0]
                    self.loadPhotosFromAlbum(categories[0].title, isUserAlbum: false)
                }
            }
        }
    }

    /// 从指定相册加载照片
    func loadPhotosFromAlbum(_ albumTitle: String, isUserAlbum: Bool) {
        isLoading = true
        photoAssets = []

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            let assets = self?.galleryService.loadPhotosFromAlbum(albumTitle, isUserAlbum: isUserAlbum) ?? []

            DispatchQueue.main.async {
                self?.photoAssets = assets
                self?.isLoading = false
            }
        }
    }

    /// 加载应用拍摄的照片
    func loadAppPhotos() {
        isLoading = true
        photoAssets = []

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            let assets = self?.galleryService.loadAppPhotos() ?? []

            DispatchQueue.main.async {
                self?.photoAssets = assets
                self?.isLoading = false
            }
        }
    }
    
    // MARK: - 图片获取
    // 图片获取已移动到 GalleryService
    
    // 原始图片获取已移动到 GalleryService
    
    // MARK: - 用户交互
    
    /// 切换主选项卡
    /// - Parameter tab: 主选项卡
    func switchMainTab(_ tab: MainGalleryTab) {
        let previousTab = selectedMainTab
        selectedMainTab = tab
        
        // 如果切换到了不同的标签，加载对应的照片
        if previousTab != tab {
            if tab == .myWorks {
                loadAppPhotos()
            } else if let album = selectedAlbum {
                loadPhotosFromAlbum(album.title, isUserAlbum: false)
            } else {
                // 没有选中相册时，加载相机胶卷
                loadPhotosFromAlbum("相机胶卷", isUserAlbum: false)
            }
        }
    }
    
    /// 选择相册
    /// - Parameter album: 要选择的相册
    func selectAlbum(_ album: AlbumCategory) {
        selectedAlbum = album
        loadPhotosFromAlbum(album.title, isUserAlbum: true)
    }
    
    /// 选择照片（设置预览索引）
    /// - Parameter index: 照片索引
    func selectPhoto(at index: Int) {
        selectedPhotoIndex = index
    }
    
    /// 取消照片选择
    func deselectPhoto() {
        selectedPhotoIndex = nil
    }
    
    /// 更新选中的水印/编辑类别
    /// - Parameter category: 类别
    func updateSelectedCategory(_ category: WatermarkCategory) {
        selectedCategory = category
    }
    
    /// 切换选择模式
    /// - Parameter isEnabled: 是否启用选择模式
    func toggleSelectionMode(_ isEnabled: Bool) {
        isSelectionMode = isEnabled
        if !isEnabled {
            // 退出选择模式时清空选中的照片
            selectedAssets.removeAll()
        }
    }
    
    /// 选择或取消选择照片
    /// - Parameter asset: 照片资源
    func togglePhotoSelection(_ asset: PHAsset) {
        // 如果是拼图水印选择模式
        if isPuzzleWatermarkMode {
            // 获取当前激活的水印类型
            let watermarkSettings = watermarkService.getSettings()
            let watermarkType = watermarkSettings.activeWatermarkStyleType
            
            // 获取当前水印类型所需的照片数量
            let requiredPhotoCount = getRequiredPhotoCount(for: watermarkType)
            
            // 如果当前照片已被选中，则可以取消选择
            if selectedAssets.contains(where: { $0.localIdentifier == asset.localIdentifier }) {
                selectedAssets.removeAll { $0.localIdentifier == asset.localIdentifier }
                // 不显示任何提示
            } else {
                // 如果已经选择了所需数量的照片，不允许再选择
                if selectedAssets.count >= requiredPhotoCount {
                    return // 不执行选择操作，也不显示提示
                }
                
                // 添加照片到选中列表
                selectedAssets.append(asset)
                // 不显示任何提示
            }
        } else {
            // 常规水印类型的原始逻辑
            if selectedAssets.contains(where: { $0.localIdentifier == asset.localIdentifier }) {
                // 取消选择
                selectedAssets.removeAll { $0.localIdentifier == asset.localIdentifier }
            } else {
                // 选择照片
                selectedAssets.append(asset)
            }
        }
    }
    
    /// 获取当前选中的照片列表
    /// - Returns: 选中的照片资源数组
    func getSelectedAssets() -> [PHAsset] {
        return selectedAssets
    }
    
    /// 检查照片是否被选中
    /// - Parameter asset: 照片资源
    /// - Returns: 是否被选中
    func isAssetSelected(_ asset: PHAsset) -> Bool {
        return selectedAssets.contains { $0.localIdentifier == asset.localIdentifier }
    }
    
    /// 返回当前是否处于拼图水印选择模式
    /// - Returns: 是否是拼图水印选择模式
    func isPuzzleWatermarkSelectionMode() -> Bool {
        return isPuzzleWatermarkMode
    }
    
    /// 返回当前是否处于拼图水印选择模式（兼容旧代码）
    /// - Returns: 是否是拼图水印选择模式
    func isCustom23SelectionMode() -> Bool {
        return isPuzzleWatermarkMode
    }
    
    /// 返回当前拼图水印照片选择情况的描述
    /// - Returns: 格式为"已选择/总需要"的字符串
    func getPuzzleWatermarkSelectionCountText() -> String {
        // 获取当前激活的水印类型
        let watermarkSettings = watermarkService.getSettings()
        let watermarkType = watermarkSettings.activeWatermarkStyleType
        
        // 获取当前水印类型所需的照片数量
        let requiredPhotoCount = getRequiredPhotoCount(for: watermarkType)
        
        return "\(selectedAssets.count)/\(requiredPhotoCount)"
    }
    
    /// 返回当前拼图水印照片选择情况的描述（兼容旧代码）
    /// - Returns: 格式为"已选择/总需要"的字符串
    func getCustom23SelectionCountText() -> String {
        return getPuzzleWatermarkSelectionCountText()
    }
    
    /// 判断是否已达到拼图水印所需的照片数量
    /// - Returns: 是否已选择足够数量的照片
    func hasEnoughPhotosForPuzzleWatermark() -> Bool {
        // 获取当前激活的水印类型
        let watermarkSettings = watermarkService.getSettings()
        let watermarkType = watermarkSettings.activeWatermarkStyleType
        
        // 获取当前水印类型所需的照片数量
        let requiredPhotoCount = getRequiredPhotoCount(for: watermarkType)
        
        return selectedAssets.count >= requiredPhotoCount
    }
    
    /// 判断是否已达到拼图水印所需的照片数量（兼容旧代码）
    /// - Returns: 是否已选择足够数量的照片
    func hasEnoughPhotosForCustom23() -> Bool {
        return hasEnoughPhotosForPuzzleWatermark()
    }
    
    /// 获取指定水印类型所需的照片数量
    /// - Parameter watermarkType: 水印类型
    /// - Returns: 所需的照片数量
    private func getRequiredPhotoCount(for watermarkType: String) -> Int {
        switch watermarkType {
        case "custom23":
            return custom23RequiredPhotoCount // 4张
        case "custom24":
            return custom24RequiredPhotoCount // 5张 - 允许最多选择5张
        default:
            return 1 // 默认为1张
        }
    }
    
    // 应用照片加载已移动到 GalleryService
    
    // MARK: - 照片选择功能
    
    /// 全选当前相册中的照片
    func selectAllAssets() {
        selectedAssets = photoAssets
    }
    
    /// 取消全部选择
    func deselectAllAssets() {
        selectedAssets.removeAll()
    }

    /// 删除选中的照片
    func deleteSelectedAssets(completion: @escaping (Bool) -> Void) {
        guard !selectedAssets.isEmpty else {
            completion(false)
            return
        }

        galleryService.deleteSelectedAssets(selectedAssets) { [weak self] success in
            if success {
                // 从当前显示的照片中移除已删除的照片
                self?.photoAssets.removeAll { asset in
                    self?.selectedAssets.contains { $0.localIdentifier == asset.localIdentifier } ?? false
                }
                self?.selectedAssets.removeAll()
                self?.isSelectionMode = false
            }
            completion(success)
        }
    }
    
    // 照片删除已移动到 GalleryService
} 