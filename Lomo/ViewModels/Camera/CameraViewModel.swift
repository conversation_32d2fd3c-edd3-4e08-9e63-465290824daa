import Foundation
import AVFoundation
import Combine
import UIKit
import SwiftUI

class CameraViewModel: NSObject, ObservableObject {
    // MARK: - Published State
    @Published var state = CameraState()
    // UI状态已移至UIStateManager
    @Published var isLocked: Bool = false           // 锁定状态
    @Published var isRecordingPaused: Bool = false  // 录制暂停状态
    
    // 用户状态 - 是否是Pro用户
    @Published var isProUser: Bool = false  // 默认为非Pro用户
    
    // MARK: - 特效功能已迁移到 EffectsViewModel
    // 注意：特效相关属性和方法已迁移到新架构的 EffectsViewModel 中


    // MARK: - Published UI States
    @Published private(set) var isGridEnabled: Bool = false
    @Published private(set) var isHistogramEnabled: Bool = false
    @Published private(set) var isLevelEnabled: Bool = false
    @Published private(set) var isFlipped: Bool = false
    @Published private(set) var isPeakingEnabled: Bool = false
    @Published private(set) var isTimerEnabled: Bool = false  // 添加定时器状态
    @Published private(set) var isHDREnabled: Bool = false
    @Published private(set) var isZebraEnabled: Bool = false
    @Published private(set) var isStabilizationEnabled: Bool = false  // 添加防抖状态
    
    // MARK: - 服务依赖
    internal var cameraService: CameraServiceProtocol
    internal var exposureService: ExposureServiceProtocol
    internal var zoomService: ZoomServiceProtocol
    internal var uiControlService: UIControlServiceProtocol
    internal var recordingService: RecordingServiceProtocol
    internal let buttonLogic = ButtonControlLogic()
    internal let animationService: CameraAnimationService
    internal let eventHandler: CameraEventHandler
    
    // 添加UI状态管理器
    internal let uiStateManager: UIStateManager
    
    // 添加相机基础设置管理器
    private let cameraBasicManager = CameraBasicManager.shared
    
    // 添加存储服务
    private let storageService = UserDefaultsService.shared
    
    // 添加相机设置存储服务
    private let settingsStorageService = CameraSettingsStorageService.shared
    
    // 添加特效服务
    private let effectsService = EffectsService.shared
    
    // 添加格式化服务
    private let formattingService = FormattingService.shared
    
    // 添加UI样式服务
    private let uiStyleService = UIStyleService.shared
    
    // 添加图像分析服务
    private let imageAnalysisService = ImageAnalysisService.shared
    
    // 添加计时器服务
    private let timerService = TimerService.shared
    
    // 添加取消器集合，用于管理订阅
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Computed Properties
    var currentOptions: [String] {
        buttonLogic.currentOptions
    }
    
    // 获取选项类型
    func getOptionType(_ option: String) -> ButtonControlLogic.ParameterOptionType {
        buttonLogic.getOptionType(option)
    }
    
    // 添加对UI状态管理器属性的便捷访问
    var isRotating: Bool {
        get { uiStateManager.isRotating }
    }
    
    var rotationAngle: Double {
        get { uiStateManager.rotationAngle }
    }
    
    var isFilterPressed: Bool {
        get { uiStateManager.isFilterPressed }
        set { uiStateManager.updateButtonPressState(button: .filter, isPressed: newValue) }
    }
    
    var isEditPressed: Bool {
        get { uiStateManager.isEditPressed }
        set { uiStateManager.updateButtonPressState(button: .edit, isPressed: newValue) }
    }
    
    var isLockPressed: Bool {
        get { uiStateManager.isLockPressed }
        set { uiStateManager.updateButtonPressState(button: .lock, isPressed: newValue) }
    }
    
    // MARK: - 初始化
    init(
        cameraService: CameraServiceProtocol? = nil,
        exposureService: ExposureServiceProtocol? = nil,
        zoomService: ZoomServiceProtocol? = nil,
        uiControlService: UIControlServiceProtocol? = nil,
        recordingService: RecordingServiceProtocol? = nil
    ) {
        // 使用提供的服务或创建默认服务
        let services = ServiceFactory.createServices()
        
        self.cameraService = cameraService ?? services.cameraService
        self.exposureService = exposureService ?? services.exposureService
        self.zoomService = zoomService ?? services.zoomService
        self.uiControlService = uiControlService ?? services.uiControlService
        self.recordingService = recordingService ?? services.recordingService
        
        // 初始化辅助组件
        self.eventHandler = CameraEventHandler(viewModel: nil)
        
        // 初始化动画视图模型
        let animationVM = CameraAnimationViewModel(state: CameraState())
        self.animationService = CameraAnimationService(viewModel: nil)
        
        // 初始化UI状态管理器
        self.uiStateManager = UIStateManager(animationViewModel: animationVM)
        
        // 清除上次保存的刻度盘角度
        settingsStorageService.clearAllDialRotationAngles()
        
        super.init()
        
        // 设置依赖关系
        self.eventHandler.viewModel = self
        self.animationService.viewModel = self
        self.uiStateManager.setCameraViewModel(self)
        
        // 订阅UIStateManager的状态变化
        uiStateManager.rotatingPublisher
            .receive(on: RunLoop.main)
            .sink { [weak self] isRotating in
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)
        
        uiStateManager.rotationAnglePublisher
            .receive(on: RunLoop.main)
            .sink { [weak self] rotationAngle in
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)
        
        // 设置UIControlService的ViewModel引用
        if let uiControlService = self.uiControlService as? UIControlService {
            uiControlService.setViewModel(self)
        }
        
        // 从持久化存储加载基本设置
        loadSavedBasicSettings()
        
        // 设置回调
        setupCallbacks()
        
        // 初始化相机
        self.cameraService.setup()

        // 监听Pro用户状态变化通知
        let notificationService = NotificationService.shared
        notificationService.addObserver(
            self,
            selector: #selector(handleProUserStatusChanged),
            name: Notification.Name("ProUserStatusChanged"),
            object: nil
        )
    }
    
    // 处理Pro用户状态变化通知
    @objc private func handleProUserStatusChanged(_ notification: Notification) {
        if let isProUser = notification.userInfo?["isProUser"] as? Bool {
            DispatchQueue.main.async {
                self.isProUser = isProUser
            }
        }
    }
    
    deinit {
        // 移除通知观察者
        NotificationService.shared.removeObserver(self)
    }
    
    // MARK: - 持久化方法
    
    /// 从持久化存储加载基本设置
    private func loadSavedBasicSettings() {
        settingsStorageService.loadSavedBasicSettings(
            into: &state, 
            isGridEnabled: &isGridEnabled,
            isHistogramEnabled: &isHistogramEnabled,
            isLevelEnabled: &isLevelEnabled,
            isPeakingEnabled: &isPeakingEnabled,
            isFlipped: &isFlipped,
            isZebraEnabled: &isZebraEnabled,
            isStabilizationEnabled: &isStabilizationEnabled,
            isHDREnabled: &isHDREnabled,
            isTimerEnabled: &isTimerEnabled,
            buttonLogic: buttonLogic
        )
    }
    
    /// 保存当前的相机基本设置
    private func saveBasicSettings() {
        settingsStorageService.saveBasicSettings(isVideoMode: state.isVideoMode)
    }
    
    // MARK: - 回调设置
    
    /// 设置所有回调
    internal func setupCallbacks() {
        setupCameraServiceCallbacks()
        setupExposureServiceCallbacks()
        setupZoomServiceCallbacks()
        setupUIControlServiceCallbacks()
        setupRecordingServiceCallbacks()
    }
    
    // MARK: - Audio Processing Methods
    
    internal func setupAudioProcessing() {
        // 音频处理初始化，以后实现
    }
    
    internal func startAudioLevelMonitoring() {
        // 开始音频电平监控，以后实现
    }
    
    internal func stopAudioLevelMonitoring() {
        // 停止音频电平监控，以后实现
    }
    
    internal func updateAudioLevels() {
        // 更新音频电平数据，以后实现
    }
    
    // MARK: - Public Methods
    
    func toggleFlash() {
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            switch state.flashMode {
            case .off:
                state.flashMode = .on
            case .on:
                state.flashMode = .auto
            case .auto:
                state.flashMode = .off
            }
        }
        // 这里可以添加实际的相机闪光灯设置代码
        print("📸 闪光灯状态: \(state.flashMode)")
    }
    
    func switchCamera() {
        // 如果正在录制或处理中，不允许切换
        if state.isRecording {
            return
        }
        
        // 切换摄像头
        let newPosition: AVCaptureDevice.Position = state.currentPosition == .back ? .front : .back
        
        // 更新UI
        executeOnMain {
            withAnimation(.spring(response: AnimationConstants.duration, 
                                  dampingFraction: AnimationConstants.dampingFraction)) {
                self.uiStateManager.isRotating = true
                self.uiStateManager.rotationAngle += 180
            }
        }
        
        // 执行相机切换，不传参数
        cameraService.switchCamera()
        
        // 更新currentPosition，因为switchCamera方法不接受参数
        state.currentPosition = newPosition
        
        // 0.5秒后重置旋转状态
        DispatchQueue.main.asyncAfter(deadline: .now() + UIConstants.cameraRotationResetDelay) {
            self.uiStateManager.isRotating = false
        }
    }
    
    func selectLens(_ lens: String) {
        zoomService.selectLens(lens)  // 修改为使用zoomService
    }
    
    func capturePhoto() {
        cameraService.capturePhoto()
    }
    
    func getCurrentDevice() -> AVCaptureDevice? {
        cameraService.getCurrentDevice()
    }
    
    // MARK: - UI State Methods
    
    func toggleVideoMode() {
        state.isVideoMode.toggle()
        
        // 保存到持久化存储
        settingsStorageService.updateVideoMode(state.isVideoMode)
        
        // 更新UI和相机设置
        buttonLogic.setVideoMode(state.isVideoMode)
        
        // 重置相关状态
        resetDialState()
        resetParameterState()
        
        // 如果在录制中，停止录制
        if state.isRecording {
            toggleRecording()
        }
    }
    
    // 重置刻度盘状态
    private func resetDialState() {
        state.isDialVisible = false
    }
    
    // 重置参数面板状态
    private func resetParameterState() {
        state.isParameterExpanded = false
    }
    
    func toggleLivePhoto() {
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            state.isLivePhotoEnabled.toggle()
            
            // 如果开启了Live Photo而HDR也是开启状态，则自动关闭HDR
            if state.isLivePhotoEnabled && isHDREnabled {
                isHDREnabled = false
            }
        }
        // 这里可以添加实际的相机Live Photo设置代码
        print("📸 Live Photo状态: \(state.isLivePhotoEnabled ? "开启" : "关闭")")
    }
    
    func toggleRecording() {
        Task {
            do {
                try await recordingService.toggleRecording()
            } catch {
                print("Recording toggle failed: \(error)")
            }
        }
    }
    
    // MARK: - 参数面板控制
    
    // 切换参数面板显示状态
    func toggleParameter() {
        if state.isParameterExpanded {
            collapseParameter()
        } else {
            expandParameter()
        }
    }
    
    // 展开参数面板
    func expandParameter() {
        // 如果正在录制，不允许展开
        if state.isRecording {
            return
        }
        
        state.isParameterExpanded = true
        state.closeButtonOpacity = 1
        
        // 延迟隐藏侧边按钮
        DispatchQueue.main.asyncAfter(deadline: .now() + AnimationConstants.quickDuration) {
            self.state.sideButtonsOpacity = 0
        }
        
        // 使用计时器服务启动参数面板的计时器
        timerService.startParameterTimer { [weak self] in
            guard let self = self else { return }
            self.state.isParameterExpanded = false
            self.state.closeButtonOpacity = 0
            self.state.sideButtonsOpacity = 1
        }
    }
    
    // 收起参数面板
    func collapseParameter() {
        state.isParameterExpanded = false
        state.closeButtonOpacity = 0
        state.sideButtonsOpacity = 1
        
        // 取消计时器
        timerService.cancelParameterTimer()
    }
    
    // MARK: - Timer Reset Methods
    
    func resetParameterTimer() {
        print("⏰ 参数面板：准备重置计时器，当前展开状态=\(state.isParameterExpanded)")
        if state.isParameterExpanded {
            timerService.resetParameterTimer(isExpanded: true) { [weak self] in
                guard let self = self else { return }
                self.state.isParameterExpanded = false
                self.state.closeButtonOpacity = 0
                self.state.sideButtonsOpacity = 1
            }
        } else {
            print("⏰ 参数面板：面板未展开，跳过重置")
        }
    }
    
    func resetLeftButtonTimer() {
        print("⏰ 3×3按钮：准备重置计时器，当前展开状态=\(state.isLeftButtonExpanded)")
        if state.isLeftButtonExpanded {
            timerService.resetLeftButtonTimer(isExpanded: true) { [weak self] in
                guard let self = self else { return }
                self.state.isLeftButtonExpanded = false
            }
        } else {
            print("⏰ 3×3按钮：按钮未展开，跳过重置")
        }
    }
    
    func resetRightButtonTimer() {
        print("⏰ A/M按钮：准备重置计时器，当前展开状态=\(state.isRightButtonExpanded)")
        if state.isRightButtonExpanded {
            timerService.resetRightButtonTimer(isExpanded: true) { [weak self] in
                guard let self = self else { return }
                let wasAMMode = self.state.isAMMode
                self.state.isRightButtonExpanded = false
                // 记录上一次状态
                self.state.previousRightButtonState = wasAMMode
            }
        } else {
            print("⏰ A/M按钮：按钮未展开，跳过重置")
        }
    }
    
    // MARK: - Tint Dial Methods
    
    func showTintDial() {
        print("📱 显示色调刻度盘")
        // 保存当前右侧按钮状态
        state.previousRightButtonState = state.isRightButtonExpanded
        // 显示色调刻度盘
        uiControlService.showDial(mode: .tint)
    }
    
    func updateTint(_ value: Double) {
        print("📱 更新色调值：\(value)")
        state.previousTintValue = state.tintValue
        state.tintValue = value
        
        // 切换到手动模式
        switchToManualMode()
        
        // 检查是否需要切换回A模式
        checkAndUpdateAMMode()
        
        // 这里可以添加更新相机色调的代码
    }
    
    // MARK: - ISO Dial Methods
    
    func showISODial() {
        print("📱 显示ISO刻度盘")
        // 保存当前右侧按钮状态
        state.previousRightButtonState = state.isRightButtonExpanded
        // 显示ISO刻度盘
        uiControlService.showDial(mode: .iso)
    }
    
    // 设置ISO值
    func setISOValue(_ value: Double) {
        // 确保ISO值在设备支持范围内
        let minISO = state.deviceMinISO
        let maxISO = state.deviceMaxISO
        let clampedValue = max(minISO, min(value, maxISO))
        
        state.isoValue = clampedValue
        state.previousISOValue = clampedValue
        
        // 切换到手动模式
        switchToManualMode()
        
        // 设置相机ISO
        cameraService.setISO(Float(clampedValue))
    }
    
    func updateISO(_ value: Double) {
        // 更新 ISO 值
        state.isoValue = value
        
        // 切换到手动模式
        switchToManualMode()
        
        // 检查是否需要切换回A模式
        checkAndUpdateAMMode()
        
        // 更新相机设置
        if let device = getCurrentDevice() {
            do {
                try device.lockForConfiguration()
                device.setExposureModeCustom(
                    duration: device.exposureDuration,
                    iso: Float(value)
                )
                device.unlockForConfiguration()
            } catch {
                print("📸 Error setting ISO: \(error)")
            }
        }
    }
    
    // 添加一个辅助方法用于切换到手动模式
    private func switchToManualMode() {
        if !state.isAMMode {
            state.isAMMode = true
        }
    }
    
    // 添加一个方法检查是否所有参数都是自动值，如果是则切换回A模式
    private func checkAndUpdateAMMode() {
        // 检查所有可调整的参数是否都是自动值
        let isAllAuto = abs(state.tintValue) < CameraThresholds.tintThreshold && 
                        abs(state.temperatureValue - CameraThresholds.temperatureBaseValue) < CameraThresholds.temperatureThreshold && 
                        abs(state.exposureValue) < CameraThresholds.exposureThreshold && 
                        abs(state.isoValue - CameraThresholds.isoBaseValue) < CameraThresholds.isoThreshold && 
                        abs(state.shutterValue - ShutterDialUtils.defaultShutterValue) < CameraThresholds.shutterThreshold && 
                        abs(state.focusDistance - CameraThresholds.focusBaseValue) < CameraThresholds.focusThreshold
        
        // 如果所有参数都是自动值，切换回A模式
        if isAllAuto && state.isAMMode {
            print("📱 所有参数都是自动值，切换回A模式")
            state.isAMMode = false
        }
    }
    
    // MARK: - Zoom Dial Methods
    
    func showZoomDial() {
        zoomService.showZoomDial()
    }
    
    func hideZoomDial() {
        zoomService.hideZoomDial()
    }
    
    func updateZoom(_ zoomFactor: CGFloat) {
        zoomService.updateZoom(zoomFactor)
    }
    
    func selectLensAtIndex(_ index: Int) {
        zoomService.selectLensAtIndex(index)
    }
    
    // MARK: - Touch State Methods
    
    func handleTouchBegan() {
        uiControlService.handleTouchBegan()
    }
    
    func handleTouchEnded() {
        uiControlService.handleTouchEnded()
    }
    
    // MARK: - 焦距选择器按钮控制
    
    // 左侧按钮（3×3按钮）切换
    func toggleLeftButton() {
        // 如果右侧按钮已展开，先收起
        if state.isRightButtonExpanded {
            state.isRightButtonExpanded = false
        }
        
        // 切换状态
        state.isLeftButtonExpanded.toggle()
        
        if state.isLeftButtonExpanded {
            // 使用计时器服务启动左侧按钮计时器
            timerService.startLeftButtonTimer { [weak self] in
                guard let self = self else { return }
                self.state.isLeftButtonExpanded = false
            }
        } else {
            // 取消计时器
            timerService.cancelLeftButtonTimer()
        }
    }
    
    // 右侧按钮（A/M按钮）切换
    func toggleRightButton() {
        // 如果左侧按钮已展开，先收起
        if state.isLeftButtonExpanded {
            state.isLeftButtonExpanded = false
        }
        
        // 切换状态
        state.isRightButtonExpanded.toggle()
        
        if state.isRightButtonExpanded {
            // 使用计时器服务启动右侧按钮计时器
            timerService.startRightButtonTimer { [weak self] in
                guard let self = self else { return }
                let wasAMMode = self.state.isAMMode
                self.state.isRightButtonExpanded = false
                // 记录上一次状态
                self.state.previousRightButtonState = wasAMMode
            }
        } else {
            // 取消计时器
            timerService.cancelRightButtonTimer()
        }
    }
    
    // MARK: - 左侧功能按钮控制
    
    // 参考线切换
    func toggleGrid() {
        buttonLogic.toggleGrid(currentState: isGridEnabled) { [weak self] newState in
            guard let self = self else { return }
            self.isGridEnabled = newState
            
            // 使用新的存储服务
            self.settingsStorageService.updateGridEnabled(newState)
        }
    }
    
    // 峰值对焦切换
    func togglePeaking() {
        buttonLogic.togglePeaking(currentState: isPeakingEnabled) { [weak self] newState in
            guard let self = self else { return }
            self.isPeakingEnabled = newState
            
            // 使用新的存储服务
            self.settingsStorageService.updatePeakingEnabled(newState)
        }
    }
    
    // 直方图切换
    func toggleHistogram() {
        buttonLogic.toggleHistogram(currentState: isHistogramEnabled) { [weak self] newState in
            guard let self = self else { return }
            self.isHistogramEnabled = newState
            
            // 使用新的存储服务
            self.settingsStorageService.updateHistogramEnabled(newState)
        }
    }
    
    // 水平仪切换
    func toggleLevel() {
        buttonLogic.toggleLevel(currentState: isLevelEnabled) { [weak self] newState in
            guard let self = self else { return }
            self.isLevelEnabled = newState
            
            // 使用新的存储服务
            self.settingsStorageService.updateLevelEnabled(newState)
        }
    }
    
    // 翻转切换
    func toggleFlip() {
        buttonLogic.toggleFlip(currentState: isFlipped) { [weak self] newState in
            guard let self = self else { return }
            self.isFlipped = newState
            
            // 使用新的存储服务
            self.settingsStorageService.updateFlippedEnabled(newState)
        }
    }
    
    // HDR切换（照片模式）
    func toggleHDR() {
        // 如果当前HDR是关闭状态，并且用户要开启HDR
        if !isHDREnabled {
            // 检查当前照片格式是否支持HDR
            let isHDRCompatible = [.jpg, .heif, .tiff].contains(state.photoFormatMode)
            
            // 如果当前格式不支持HDR但HDR已开启，则关闭HDR
            if !isHDRCompatible {
                // RAW和ProRAW格式自动切换到TIFF
                if [.raw, .proRaw].contains(state.photoFormatMode) {
                    selectPhotoFormatMode(.tiff)
                } 
                // PNG格式自动切换到JPG
                else if state.photoFormatMode == .png {
                    selectPhotoFormatMode(.jpg)
                }
            }
        }
        
        // 不再检查格式兼容性，允许任何情况下都可以切换HDR状态
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            isHDREnabled.toggle()  // 切换状态
            
            // 使用新的存储服务
            settingsStorageService.updateHDREnabled(isHDREnabled)
            
            // 如果开启了HDR而Live Photo也是开启状态，则自动关闭Live Photo
            if isHDREnabled && state.isLivePhotoEnabled {
                state.isLivePhotoEnabled = false
            }
            
            buttonLogic.toggleHDR(currentState: isHDREnabled, isVideoMode: state.isVideoMode) { [weak self] newState in
                guard let self = self else { return }
                self.isHDREnabled = newState
                
                // 使用新的存储服务
                self.settingsStorageService.updateHDREnabled(newState)
            }
        }
    }
    
    // 斑马线切换（视频模式）
    func toggleZebra() {
        buttonLogic.toggleZebra(currentState: isZebraEnabled, isVideoMode: state.isVideoMode) { [weak self] newState in
            guard let self = self else { return }
            self.isZebraEnabled = newState
            
            // 使用新的存储服务
            self.settingsStorageService.updateZebraEnabled(newState)
        }
    }
    
    // 防抖切换
    func toggleStabilization() {
        // 不直接切换isStabilizationEnabled状态
        // 而是只负责显示/隐藏选项面板
        
        buttonLogic.toggleStabilization(currentState: isStabilizationEnabled, isVideoMode: state.isVideoMode) { [weak self] _ in
            guard let self = self else { return }
            
            if state.isStabilizationOptionsVisible {
                // 如果当前是显示状态，就隐藏
                state.isStabilizationOptionsVisible = false
            } else {
                // 如果当前是隐藏状态，先隐藏其他选项，再显示当前选项
                hideAllBottomOptions()
                state.isStabilizationOptionsVisible = true
                
                // 启动自动隐藏计时器
                buttonLogic.startOptionPanelTimer(type: .bottomOptionPanel) { [weak self] in
                    self?.state.isStabilizationOptionsVisible = false
                }
            }
            
            // 不在这里更改isStabilizationEnabled状态
            // 状态只在选择具体模式时更改
        }
    }
    
    // 选择防抖模式
    func selectStabilizationMode(_ mode: StabilizationMode) {
        // 立即更新状态，不等待胶囊收回
        state.stabilizationMode = mode
            
        // 关键逻辑：只有在模式不是"off"时才启用防抖
        isStabilizationEnabled = (mode != .off)
            
        // 保存到持久化存储
        settingsStorageService.updateStabilization(mode: mode, isEnabled: isStabilizationEnabled)
            
        // 选择后自动隐藏选项面板
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            state.isStabilizationOptionsVisible = false
        }
            
        // 重置参数面板计时器
        resetParameterTimer()
        
        // 确保UI更新
        objectWillChange.send()
        print("📱 防抖模式已更新: \(mode.rawValue), 防抖状态: \(isStabilizationEnabled)")
    }
    
    // 定时器切换（照片模式）
    func toggleTimer() {
        buttonLogic.toggleTimer(currentState: isTimerEnabled, isVideoMode: state.isVideoMode) { [weak self] newState in
            self?.isTimerEnabled = newState
            
            // 如果关闭定时器，将定时器模式设为off
            if !newState {
                self?.state.timerMode = .off
            }
        }
    }
    
    // MARK: - Recording Timer Methods
    
    // 委托给格式化服务
    internal func formattedTime(_ time: TimeInterval) -> String {
        // 使用格式化服务代替直接调用
        return formattingService.formatTime(time)
    }

    // 计算属性 formattedRecordingTime 保持不变，它会调用上面重构后的 formattedTime
    var formattedRecordingTime: String {
        formattedTime(state.recordingTime)
    }
    
    // MARK: - Animation Helpers
    
    private func animate(_ action: @escaping () -> Void) {
        buttonLogic.animate(action)
    }
    
    // MARK: - Exposure Dial Methods
    
    func showExposureDial() {
        // 保存当前右侧按钮状态
        state.previousRightButtonState = state.isRightButtonExpanded
        // 显示曝光刻度盘
        uiControlService.showDial(mode: .exposure)
    }
    
    func updateExposure(_ value: Double) {
        print("📸 更新曝光值 - 旧值: \(state.exposureValue), 新值: \(value)")
        state.exposureValue = value
        state.previousExposureValue = value
        
        // 切换到手动模式
        switchToManualMode()
        
        // 检查是否需要切换回A模式
        checkAndUpdateAMMode()
        
        // 设置相机曝光补偿
        cameraService.setExposureCompensation(value)
    }
    
    func updateExposureDialRotation(_ angle: Double) {
        state.exposureDialRotationAngle = angle
    }
    
    // MARK: - Temperature Dial Methods
    
    func showTemperatureDial() {
        print("📱 显示色温刻度盘")
        state.previousRightButtonState = state.isRightButtonExpanded
        uiControlService.showDial(mode: .temperature)
    }
    
    func updateTemperature(_ value: Double) {
        print("📱 更新色温值：\(value)")
        state.temperatureValue = value
        state.previousTemperatureValue = value
        
        // 切换到手动模式
        switchToManualMode()
        
        // 检查是否需要切换回A模式
        checkAndUpdateAMMode()
        
        cameraService.setTemperature(value)
    }
    
    func updateTemperatureDialRotation(_ angle: Double) {
        state.temperatureDialRotationAngle = angle
    }
    
    // MARK: - Shutter Dial Methods
    
    func showShutterDial() {
        print("📱 显示快门刻度盘")
        // 保存当前右侧按钮状态
        state.previousRightButtonState = state.isRightButtonExpanded
        // 显示快门刻度盘
        uiControlService.showDial(mode: .shutter)
    }
    
    func updateShutter(_ value: Double) {
        print("📸 更新快门值 - 旧值: \(state.shutterValue), 新值: \(value)")
        state.shutterValue = value
        state.previousShutterValue = value
        
        // 切换到手动模式
        switchToManualMode()
        
        // 检查是否需要切换回A模式
        checkAndUpdateAMMode()
        
        // 设置相机快门速度
        cameraService.setShutterSpeed(value)
        print("📸 快门值已更新 - 当前值: \(state.shutterValue), 格式化显示: \(ShutterDialUtils.formatShutterValue(state.shutterValue))")
        
        // 确保 UI 更新
        objectWillChange.send()
    }
    
    // MARK: - Focus Dial Methods
    
    func showFocusDial() {
        // 保存当前右侧按钮状态
        state.previousRightButtonState = state.isRightButtonExpanded
        // 显示焦点刻度盘
        uiControlService.showDial(mode: .focus)
    }
    
    func updateFocus(_ value: Double) {
        state.focusDistance = value
        
        // 切换到手动模式
        switchToManualMode()
        
        // 检查是否需要切换回A模式
        checkAndUpdateAMMode()
        
        cameraService.setFocus(value)
    }
    
    func updateFocusDialRotation(_ angle: Double) {
        state.focusDialRotationAngle = angle
    }
    
    // MARK: - Aperture Dial Methods
    
    func showApertureDial() {
        // 保存当前右侧按钮状态
        state.previousRightButtonState = state.isRightButtonExpanded
        // 显示光圈刻度盘
        uiControlService.showDial(mode: .aperture)
        
        // 更新光圈值
        if let device = getCurrentDevice() {
            state.apertureValue = Double(device.lensAperture)
        }
    }
    
    // 翻转相关方法
    func toggleFlipOptions() {
        if state.isFlipOptionsVisible {
            // 如果当前是显示状态，就隐藏
            state.isFlipOptionsVisible = false
        } else {
            // 如果当前是隐藏状态，先隐藏其他选项，再显示当前选项
            hideAllBottomOptions()
            state.isFlipOptionsVisible = true
            
            // 启动自动隐藏计时器
            buttonLogic.startOptionPanelTimer(type: .bottomOptionPanel) { [weak self] in
                self?.state.isFlipOptionsVisible = false
            }
        }
    }
    
    func selectFlipMode(_ mode: FlipMode) {
        // 立即更新状态，不等待胶囊收回
        state.flipMode = mode
        
        // 保存到持久化存储
        settingsStorageService.updateFlipMode(mode)
        
        // 选择后自动隐藏选项面板
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            state.isFlipOptionsVisible = false
        }
        
        // 重置左侧按钮计时器
        resetLeftButtonTimer()
    }
    
    // 视频宽高比相关方法
    
    // 切换宽高比选项显示状态
    func toggleAspectRatioOptions() {
        if state.isAspectRatioOptionsVisible {
            // 如果当前是显示状态，就隐藏
            state.isAspectRatioOptionsVisible = false
        } else {
            // 如果当前是隐藏状态，先隐藏其他选项，再显示当前选项
            hideAllTopOptions()
            state.isAspectRatioOptionsVisible = true
            
            // 启动自动隐藏计时器
            buttonLogic.startOptionPanelTimer(type: .topOptionPanel) { [weak self] in
                self?.state.isAspectRatioOptionsVisible = false
            }
        }
    }
    
    // 选择宽高比模式
    func selectAspectRatioMode(_ mode: AspectRatioMode) {
        // 立即更新状态，不等待胶囊收回
        state.aspectRatioMode = mode
            
        // 保存到持久化存储
        settingsStorageService.updateAspectRatioMode(mode)
            
        // 选择后自动隐藏选项面板
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            state.isAspectRatioOptionsVisible = false
        }
            
        // 重置参数面板计时器
        resetParameterTimer()
    }
    
    // 视频分辨率相关方法
    
    // 切换分辨率选项显示状态
    func toggleResolutionOptions() {
        if state.isResolutionOptionsVisible {
            // 如果当前是显示状态，就隐藏
            state.isResolutionOptionsVisible = false
        } else {
            // 如果当前是隐藏状态，先隐藏其他选项，再显示当前选项
            hideAllTopOptions()
            state.isResolutionOptionsVisible = true
            
            // 启动自动隐藏计时器
            buttonLogic.startOptionPanelTimer(type: .topOptionPanel) { [weak self] in
                self?.state.isResolutionOptionsVisible = false
            }
        }
    }
    
    // 选择分辨率模式
    func selectResolutionMode(_ mode: ResolutionMode) {
        // 立即更新状态，不等待胶囊收回
        state.resolutionMode = mode
            
        // 保存到持久化存储
        settingsStorageService.updateResolutionMode(mode)
            
        // 选择后自动隐藏选项面板
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            state.isResolutionOptionsVisible = false
        }
            
        // 重置参数面板计时器
        resetParameterTimer()
    }
    
    // 视频帧率相关方法
    
    // 切换帧率选项显示状态
    func toggleFrameRateOptions() {
        if state.isFrameRateOptionsVisible {
            // 如果当前是显示状态，就隐藏
            state.isFrameRateOptionsVisible = false
        } else {
            // 如果当前是隐藏状态，先隐藏其他选项，再显示当前选项
            hideAllTopOptions()
            state.isFrameRateOptionsVisible = true
            
            // 启动自动隐藏计时器
            buttonLogic.startOptionPanelTimer(type: .topOptionPanel) { [weak self] in
                self?.state.isFrameRateOptionsVisible = false
            }
        }
    }
    
    // 选择帧率模式
    func selectFrameRateMode(_ mode: FrameRateMode) {
        // 立即更新状态，不等待胶囊收回
        state.frameRateMode = mode
        
        // 保存到持久化存储
        settingsStorageService.updateFrameRateMode(mode)
        
        // 选择后自动隐藏选项面板
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            state.isFrameRateOptionsVisible = false
        }
        
        // 重置参数面板计时器
        resetParameterTimer()
    }
    
    // 视频色彩空间相关方法
    
    // 切换色彩空间选项显示状态
    func toggleColorSpaceOptions() {
        if state.isColorSpaceOptionsVisible {
            // 如果当前是显示状态，就隐藏
            state.isColorSpaceOptionsVisible = false
        } else {
            // 如果当前是隐藏状态，先隐藏其他选项，再显示当前选项
            hideAllTopOptions()
            state.isColorSpaceOptionsVisible = true
            
            // 启动自动隐藏计时器
            buttonLogic.startOptionPanelTimer(type: .topOptionPanel) { [weak self] in
                self?.state.isColorSpaceOptionsVisible = false
            }
        }
    }
    
    // 选择色彩空间模式
    func selectColorSpaceMode(_ mode: ColorSpaceMode) {
        // 立即更新状态，不等待胶囊收回
        state.colorSpaceMode = mode
        
        // 保存到持久化存储
        settingsStorageService.updateColorSpaceMode(mode)
        
        // 选择后自动隐藏选项面板
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            state.isColorSpaceOptionsVisible = false
        }
        
        // 重置参数面板计时器
        resetParameterTimer()
    }
    
    // 照片文件格式相关方法
    
    // 切换文件格式选项显示状态
    func togglePhotoFormatOptions() {
        if state.isPhotoFormatOptionsVisible {
            // 如果当前是显示状态，就隐藏
            state.isPhotoFormatOptionsVisible = false
        } else {
            // 如果当前是隐藏状态，先隐藏其他选项，再显示当前选项
            hideAllTopOptions()
            state.isPhotoFormatOptionsVisible = true
            
            // 启动自动隐藏计时器
            buttonLogic.startOptionPanelTimer(type: .topOptionPanel) { [weak self] in
                self?.state.isPhotoFormatOptionsVisible = false
            }
        }
    }
    
    // 选择文件格式模式
    func selectPhotoFormatMode(_ mode: PhotoFormatMode) {
        // 立即更新状态，不等待胶囊收回
        state.photoFormatMode = mode
        
        // 保存到持久化存储
        settingsStorageService.updatePhotoFormatMode(mode)
        
        // 选择后自动隐藏选项面板
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            state.isPhotoFormatOptionsVisible = false
        }
        
        // 重置参数面板计时器
        resetParameterTimer()
    }
    
    // 检查HDR与选择的照片格式的兼容性
    private func updateHDRCompatibility(with format: PhotoFormatMode) {
        // JPG、HEIF和TIFF格式支持HDR
        let isHDRCompatible = [.jpg, .heif, .tiff].contains(format)
        
        // 如果当前格式不支持HDR但HDR已开启，则关闭HDR
        if !isHDRCompatible && isHDREnabled {
            isHDREnabled = false
        }
    }
    
    // 照片比例相关方法
    
    // 切换比例选项显示状态
    func togglePhotoRatioOptions() {
        if state.isPhotoRatioOptionsVisible {
            // 如果当前是显示状态，就隐藏
            state.isPhotoRatioOptionsVisible = false
        } else {
            // 如果当前是隐藏状态，先隐藏其他选项，再显示当前选项
            hideAllTopOptions()
            state.isPhotoRatioOptionsVisible = true
            
            // 启动自动隐藏计时器
            buttonLogic.startOptionPanelTimer(type: .topOptionPanel) { [weak self] in
                self?.state.isPhotoRatioOptionsVisible = false
            }
        }
    }
    
    // 选择照片比例模式
    func selectPhotoRatioMode(_ mode: PhotoRatioMode) {
        // 立即更新状态，不等待胶囊收回
        state.photoRatioMode = mode
        
        // 保存到持久化存储
        settingsStorageService.updatePhotoRatioMode(mode)
        
        // 选择后自动隐藏选项面板
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            state.isPhotoRatioOptionsVisible = false
        }
        
        // 重置参数面板计时器
        resetParameterTimer()
    }
    
    // 照片模式相关方法
    
    // 切换照片模式选项显示状态
    func togglePhotoModeOptions() {
        if state.isPhotoModeOptionsVisible {
            // 如果当前是显示状态，就隐藏
            state.isPhotoModeOptionsVisible = false
        } else {
            // 如果当前是隐藏状态，先隐藏其他选项，再显示当前选项
            hideAllBottomOptions()
            state.isPhotoModeOptionsVisible = true
            
            // 启动自动隐藏计时器
            buttonLogic.startOptionPanelTimer(type: .bottomOptionPanel) { [weak self] in
                self?.state.isPhotoModeOptionsVisible = false
            }
        }
    }
    
    // 选择照片模式
    func selectPhotoMode(_ mode: PhotoMode) {
        // 立即更新状态，不等待胶囊收回
        state.photoMode = mode
        
        // 保存到持久化存储
        settingsStorageService.updatePhotoMode(mode)
        
        // 选择后自动隐藏选项面板
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            state.isPhotoModeOptionsVisible = false
        }
        
        // 重置左侧按钮计时器
        resetLeftButtonTimer()
    }
    
    // 定时器相关方法
    
    // 切换定时器选项显示状态
    func toggleTimerOptions() {
        if state.isTimerOptionsVisible {
            // 如果当前是显示状态，就隐藏
            state.isTimerOptionsVisible = false
        } else {
            // 如果当前是隐藏状态，先隐藏其他选项，再显示当前选项
            hideAllBottomOptions()
            state.isTimerOptionsVisible = true
            
            // 启动自动隐藏计时器
            buttonLogic.startOptionPanelTimer(type: .bottomOptionPanel) { [weak self] in
                self?.state.isTimerOptionsVisible = false
            }
        }
    }
    
    // 隐藏所有底部选项面板
    private func hideAllBottomOptions() {
        state.isPhotoModeOptionsVisible = false
        state.isFlipOptionsVisible = false
        state.isStabilizationOptionsVisible = false
        state.isTimerOptionsVisible = false
    }

    // 隐藏所有顶部选项面板
    private func hideAllTopOptions() {
        state.isAspectRatioOptionsVisible = false
        state.isResolutionOptionsVisible = false
        state.isVideoEncodingOptionsVisible = false
        state.isFrameRateOptionsVisible = false
        state.isColorSpaceOptionsVisible = false
        state.isPhotoRatioOptionsVisible = false
        state.isPhotoFormatOptionsVisible = false
    }

    // 视频编码相关方法
    
    /// 切换视频编码选项的显示状态
    func toggleVideoEncodingOptions() {
        if state.isVideoEncodingOptionsVisible {
            // 如果当前是显示状态，就隐藏
            state.isVideoEncodingOptionsVisible = false
        } else {
            // 如果当前是隐藏状态，先隐藏其他选项，再显示当前选项
            hideAllTopOptions()
            state.isVideoEncodingOptionsVisible = true
            
            // 启动自动隐藏计时器
            buttonLogic.startOptionPanelTimer(type: .topOptionPanel) { [weak self] in
                self?.state.isVideoEncodingOptionsVisible = false
            }
        }
    }
    
    /// 选择视频编码模式
    func selectVideoEncodingMode(_ mode: VideoEncodingMode) {
        // 立即更新状态，不等待胶囊收回
        state.videoEncodingMode = mode
        
        // 保存到持久化存储
        settingsStorageService.updateVideoEncodingMode(mode)
        
        // 选择后自动隐藏选项面板
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            state.isVideoEncodingOptionsVisible = false
        }
        
        // 重置参数面板计时器
        resetParameterTimer()
    }

    // MARK: - View Interface Methods
    
    // 参数展开状态检查
    func isParameterExpanded() -> Bool {
        return eventHandler.shouldShowParameterExpanded()
    }
    
    // 视频模式切换
    func toggleVideoModeWithAnimation() {
        withAnimation(.spring(response: AnimationConstants.duration, dampingFraction: AnimationConstants.dampingFraction)) {
            // 尝试直接调用toggleVideoMode，这样可以绕过eventHandler
            toggleVideoMode()
        }
    }

    // MARK: - Animation Methods
    
    func getQuickSpringAnimation() -> Animation {
        return .spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)
    }
    
    func getSpringAnimation(response: Double = AnimationConstants.duration, dampingFraction: Double = AnimationConstants.dampingFraction) -> Animation {
        return animationService.getSpringAnimation(response: response, dampingFraction: dampingFraction)
    }
    
    func expandParameterWithAnimation() {
        withAnimation(AnimationConstants.standardSpring) {
            eventHandler.expandParameter()
        }
    }

    // MARK: - Parameter Option Methods
    
    func getParameterIconColor(for option: String) -> Color {
        return uiStyleService.getParameterIconColor(
            for: option,
            state: state,
            isFlashEnabled: state.flashMode != .off,
            isLivePhotoEnabled: state.isLivePhotoEnabled,
            isHDREnabled: isHDREnabled,
            isStabilizationEnabled: isStabilizationEnabled
        )
    }
    
    // 获取参数文本显示值
    func getParameterTextValue(for option: String) -> String {
        return uiStyleService.getParameterTextValue(for: option, state: state)
    }

    func getParameterFlashIcon() -> String {
        // 将CameraState.FlashMode转换为UIStyleService中的FlashMode
        let flashMode: FlashMode
        switch state.flashMode {
        case .off:
            flashMode = .off
        case .on:
            flashMode = .on
        case .auto:
            flashMode = .auto
        }
        return uiStyleService.getFlashIcon(for: flashMode)
    }

    // MARK: - Bottom Control Methods
    
    func toggleLockWithFeedback() {
        animationService.animateButtonToggle {
            self.isLocked.toggle()
        }
        // 通过 Service 处理震动反馈
        HapticService.shared.mediumImpact()
    }
    
    // 添加暂停/继续录制功能
    func toggleRecordingPauseWithFeedback() {
        // 确保只在录制状态下有效
        guard state.isVideoMode && state.isRecording else { return }
        
        animationService.animateButtonToggle {
            self.recordingService.togglePause()
            self.isRecordingPaused = self.recordingService.isPauseActive()
        }
        // 触觉反馈
        HapticService.shared.mediumImpact()
    }

    func handleShutterButtonPress() {
        // 如果参数面板展开，先收起
        if state.isParameterExpanded {
            withAnimation(getQuickSpringAnimation()) {
                eventHandler.collapseParameter()
            }
        }
        
        // 根据模式处理拍摄/录制
        if state.isVideoMode {
            eventHandler.toggleRecording()
        } else {
            eventHandler.capturePhoto()
        }
    }

    func getEditButtonState() -> EditButtonState {
        if state.isVideoMode && state.isRecording {
            return .disabled
        } else {
            return .enabled
        }
    }

    // 添加枚举来表示编辑按钮状态
    enum EditButtonState {
        case enabled
        case disabled
    }

    // MARK: - Button Control Methods

    func toggleHistogramWithTimer() {
        // 使用animationService包装操作
        animationService.animateButtonToggle {
            self.toggleHistogram()
        }
        resetLeftButtonTimer()
    }

    func toggleGridWithTimer() {
        // 使用animationService包装操作
        animationService.animateButtonToggle {
            self.toggleGrid()
        }
        resetLeftButtonTimer()
    }

    func toggleTimerOptionsWithTimer() {
        // 使用animationService包装操作
        animationService.animateButtonToggle {
            self.toggleTimerOptions()
        }
        resetLeftButtonTimer()
    }

    func toggleStabilizationWithTimer() {
        // 使用animationService包装操作
        animationService.animateButtonToggle {
            self.toggleStabilization()
        }
        resetLeftButtonTimer()
    }

    func togglePhotoModeOptionsWithTimer() {
        // 使用animationService包装操作
        animationService.animateButtonToggle {
            self.togglePhotoModeOptions()
        }
        resetLeftButtonTimer()
    }

    func toggleZebraWithTimer() {
        // 使用animationService包装操作
        animationService.animateButtonToggle {
            self.toggleZebra()
        }
        resetLeftButtonTimer()
    }

    func toggleFlipOptionsWithTimer() {
        // 使用animationService包装操作
        animationService.animateButtonToggle {
            self.toggleFlipOptions()
        }
        resetLeftButtonTimer()
    }

    func togglePeakingWithTimer() {
        // 使用animationService包装操作
        animationService.animateButtonToggle {
            self.togglePeaking()
        }
        resetLeftButtonTimer()
    }

    func toggleLevelWithTimer() {
        // 使用animationService包装操作
        animationService.animateButtonToggle {
            self.toggleLevel()
        }
        resetLeftButtonTimer()
    }

    func toggleRightButtonWithTimer() {
        // 使用animationService包装操作
        animationService.animateButtonToggle {
            self.toggleRightButton()
        }
        resetRightButtonTimer()
    }

    func showApertureDialWithTimer() {
        // 使用animationService包装操作
        animationService.animateButtonToggle {
            self.showApertureDial()
        }
        resetRightButtonTimer()
    }

    func showShutterDialWithTimer() {
        // 使用animationService包装操作
        animationService.animateButtonToggle {
            self.showShutterDial()
        }
        resetRightButtonTimer()
    }

    func showISODialWithTimer() {
        // 使用animationService包装操作
        animationService.animateButtonToggle {
            self.showISODial()
        }
        resetRightButtonTimer()
    }

    func showExposureDialWithTimer() {
        // 使用animationService包装操作
        animationService.animateButtonToggle {
            self.showExposureDial()
        }
        resetRightButtonTimer()
    }

    func showFocusDialWithTimer() {
        // 使用animationService包装操作
        animationService.animateButtonToggle {
            self.showFocusDial()
        }
        resetRightButtonTimer()
    }

    func showTemperatureDialWithTimer() {
        // 使用animationService包装操作
        animationService.animateButtonToggle {
            self.showTemperatureDial()
        }
        resetRightButtonTimer()
    }

    func showTintDialWithTimer() {
        // 使用animationService包装操作
        animationService.animateButtonToggle {
            self.showTintDial()
        }
        resetRightButtonTimer()
    }

    // MARK: - Public Interface Methods
    
    // 处理参数图标点击
    func handleParameterIconTap(option: String) {
        eventHandler.handleIconButtonTap(option: option)
    }

    // 格式化镜头值
    func formatLensValue(lens: String, isSelected: Bool, currentZoomFactor: CGFloat) -> String {
        return formattingService.formatLensValue(lens: lens, isSelected: isSelected, currentZoomFactor: currentZoomFactor)
    }
    
    // 是否应该显示预览按钮
    func shouldShowPreviewButton() -> Bool {
        eventHandler.shouldShowPreviewButton()
    }
    
    // 是否应该显示滤镜按钮
    func shouldShowFilterButton() -> Bool {
        eventHandler.shouldShowFilterButton()
    }
    
    // 是否应该显示左侧按钮
    func shouldShowLeftButton() -> Bool {
        eventHandler.shouldShowLeftButton()
    }
    
    // 是否应该显示右侧按钮
    func shouldShowRightButton() -> Bool {
        eventHandler.shouldShowRightButton()
    }
    
    // 是否应该显示视频模式控制
    func shouldShowVideoModeControls() -> Bool {
        eventHandler.shouldShowVideoModeControls()
    }
    
    // 是否应该显示相机切换按钮
    func shouldShowCameraSwitchButton() -> Bool {
        eventHandler.shouldShowCameraSwitchButton()
    }
    
    // 获取录制背景颜色
    func getRecordingBackgroundColor() -> Color {
        // 只有正在录制且没有暂停时才显示红色背景
        if isVideoRecording && !isRecordingPaused {
            return UIConstants.recordingColor
        } else {
            return Color.black.opacity(UIConstants.topBarBackgroundOpacity) // 使用标准的25%不透明度
        }
    }
    
    // 处理触摸开始
    func handleTouchBeganForDial() {
        eventHandler.handleTouchBegan()
    }
    
    // 处理触摸结束
    func handleTouchEndedForDial() {
        eventHandler.handleTouchEnded()
    }

    // MARK: - Histogram Methods

    /// 获取直方图数据
    /// - Returns: 直方图数据数组，包含256个元素，表示RGB通道的直方图
    func getHistogramData() -> [UInt8]? {
        // 使用图像分析服务获取直方图数据
        return ImageAnalysisService.shared.getHistogramData()
    }

    // 选择定时器模式
    func selectTimerMode(_ mode: TimerMode) {
        // 立即更新状态，不等待胶囊收回
        state.timerMode = mode
        
        // 关键逻辑：只有在模式不是"off"时才启用定时器
        isTimerEnabled = (mode != .off)
        
        // 保存到持久化存储
        settingsStorageService.updateTimerSettings(mode: mode, isEnabled: isTimerEnabled)
        
        // 选择后自动隐藏选项面板
        animationService.animateCollapse {
            self.state.isTimerOptionsVisible = false
        }
        
        // 重置左侧按钮计时器
        resetLeftButtonTimer()
        
        // 确保UI更新
        objectWillChange.send()
        print("📱 定时器模式已更新: \(mode.rawValue), 定时器状态: \(isTimerEnabled)")
    }

    // MARK: - 格式化属性（为符合MVVM添加）
    
    // 格式化后的曝光值
    var formattedExposureValue: String {
        return formattingService.formatExposureValue(state.exposureValue)
    }
    
    // 格式化后的对焦距离
    var formattedFocusValue: String {
        return formattingService.formatFocusValue(state.focusDistance)
    }
    
    // 格式化后的色温值
    var formattedTemperatureValue: String {
        return formattingService.formatTemperatureValue(state.temperatureValue)
    }
    
    // 格式化后的色调值
    var formattedTintValue: String {
        return formattingService.formatTintValue(state.tintValue)
    }
    
    // 格式化后的ISO值
    var formattedISOValue: String {
        return formattingService.formatISOValue(state.isoValue)
    }

    // MARK: - Computed UI Logic Properties

    /// 是否应该显示镜头选择器 (复制自 CameraView lensButtons)
    var shouldShowLensSelector: Bool {
        // 复制过来的逻辑
        !state.isLeftButtonExpanded && !state.isRightButtonExpanded
    }

    /// 是否可以显示顶栏次要控件（如切换相机按钮） (复制自 TopBarView 条件)
    var canShowSecondaryTopBarControls: Bool {
        // 复制过来的逻辑
        !state.isParameterExpanded && !state.isRecording
    }

    /// 是否正在录制视频（视频模式且正在录制） (复制自快门按钮条件)
    var isVideoRecording: Bool {
        // 复制过来的逻辑
        state.isVideoMode && state.isRecording
    }

    /// 是否可以编辑或使用其他录制时禁用的功能 (复制自编辑按钮条件)
    var canEditOrModify: Bool {
        // 复制过来的逻辑
        !state.isRecording
    }

    // 可以继续在这里添加其他封装复杂条件的计算属性
    // var shouldShowCameraSwitch: Bool { ... }

    // MARK: - 3x3功能按钮方法
    
    /// 更新网格状态
    /// - Parameter newState: 新的网格状态
    func updateGridEnabled(_ newState: Bool) {
        executeOnMain {
            self.isGridEnabled = newState
            // 持久化存储设置
            self.settingsStorageService.updateGridEnabled(newState)
        }
    }
    
    /// 更新峰值对焦状态
    /// - Parameter newState: 新的峰值对焦状态
    func updatePeakingEnabled(_ newState: Bool) {
        executeOnMain {
            self.isPeakingEnabled = newState
            // 持久化存储设置
            self.settingsStorageService.updatePeakingEnabled(newState)
        }
    }
    
    /// 更新直方图状态
    /// - Parameter newState: 新的直方图状态
    func updateHistogramEnabled(_ newState: Bool) {
        executeOnMain {
            self.isHistogramEnabled = newState
            // 持久化存储设置
            self.settingsStorageService.updateHistogramEnabled(newState)
        }
    }
    
    /// 更新水平仪状态
    /// - Parameter newState: 新的水平仪状态
    func updateLevelEnabled(_ newState: Bool) {
        executeOnMain {
            self.isLevelEnabled = newState
            // 持久化存储设置
            self.settingsStorageService.updateLevelEnabled(newState)
        }
    }
    
    /// 更新翻转状态
    /// - Parameter newState: 新的翻转状态
    func updateFlipped(_ newState: Bool) {
        executeOnMain {
            self.isFlipped = newState
            // 持久化存储设置
            self.settingsStorageService.updateFlippedEnabled(newState)
        }
    }
}

