import Foundation
import SwiftUI
import Combine

/// 相册滤镜视图模型，处理滤镜显示和交互逻辑 - MVVM-S架构
class GalleryFilterViewModel: ObservableObject {
    /// 滤镜服务
    private let filterService: FilterServiceProtocol
    
    /// 当前选中的滤镜类别
    @Published var selectedCategory: FilterCategory = .film
    
    /// 当前类别下的滤镜列表
    @Published var filters: [Filter] = []
    
    /// 是否有选中的滤镜
    @Published var hasSelectedFilter: Bool = false
    
    /// 当前选中的滤镜
    @Published var selectedFilter: Filter?
    
    // MARK: - 预设相关状态
    
    /// 选中的宝丽来预设索引
    @Published var selectedPolaroidPreset: Int = 0
    
    /// 选中的胶片预设索引
    @Published var selectedFilmPreset: Int = 0
    
    /// 选中的复古预设索引
    @Published var selectedVintagePreset: Int = 0
    
    /// 选中的时尚预设索引
    @Published var selectedFashionPreset: Int = 0
    
    /// 选中的INS风预设索引
    @Published var selectedINSPreset: Int = 0
    
    /// 当前全局活跃的滤镜类型和预设索引
    @Published var activeFilterType: String = ""
    @Published var activePresetIndex: Int = -1
    
    /// 随机种子值（用于某些随机效果）
    @Published var randomSeed: Int = Int.random(in: 0...1000)
    
    /// 初始化方法
    /// - Parameter filterService: 滤镜服务
    init(filterService: FilterServiceProtocol = GalleryFilterDependencyContainer.shared.galleryFilterService) {
        self.filterService = filterService
        
        // 初始加载胶片类别的滤镜
        loadFilters(for: .film)
    }
    
    /// 根据类别加载滤镜
    /// - Parameter category: 滤镜类别
    func loadFilters(for category: FilterCategory) {
        selectedCategory = category
        
        // 根据不同类别加载滤镜
        switch category {
        case .favorites:
            // 加载收藏的滤镜
            filters = filterService.getFavoriteFilters()
        case .film, .polaroid, .nature, .fresh, .vintage, .blackAndWhite, .custom:
            // 加载指定类型的滤镜
            if let type = category.filterType {
                filters = filterService.getFilters(byType: type)
            }
        }
    }
    
    /// 选择滤镜
    /// - Parameter filter: 要选择的滤镜
    func selectFilter(_ filter: Filter) {
        selectedFilter = filter
        hasSelectedFilter = true
    }
    
    /// 取消选择滤镜
    func deselectFilter() {
        selectedFilter = nil
        hasSelectedFilter = false
    }
    
    /// 切换滤镜收藏状态
    /// - Parameter filterId: 滤镜ID
    func toggleFavorite(filterId: String) {
        let isFavorite = filterService.toggleFavorite(filterId: filterId)
        
        // 如果当前在收藏类别中，需要更新列表
        if selectedCategory == .favorites {
            loadFilters(for: .favorites)
        }
        
        // 如果选中的滤镜被切换了收藏状态，更新选中滤镜的状态
        if let selectedFilter = selectedFilter, selectedFilter.id == filterId {
            var updatedFilter = selectedFilter
            updatedFilter.isFavorite = isFavorite
            self.selectedFilter = updatedFilter
        }
    }
    
    // MARK: - 预设相关方法
    
    /// 通用的预设选择方法，处理所有类型的预设
    /// - Parameters:
    ///   - type: 预设类型
    ///   - index: 预设索引
    func selectPreset(type: PresetType, index: Int) {
        let typeString = type.rawValue
        let filterService = FilterService.shared
        
        // 如果点击已选中的预设，则取消选择
        if activeFilterType == typeString && activePresetIndex == index {
            // 取消选择
            activeFilterType = ""
            activePresetIndex = -1
            // 更新设置
            filterService.updateSetting(\.activeFilterType, value: "")
            filterService.updateSetting(\.activePresetIndex, value: -1)
        } else {
            // 更新选中预设索引
            switch type {
            case .polaroid:
                selectedPolaroidPreset = index
            case .film:
                selectedFilmPreset = index
            case .vintage:
                selectedVintagePreset = index
            case .fashion:
                selectedFashionPreset = index
            case .ins:
                selectedINSPreset = index
            }
            
            // 更新全局活跃滤镜类型和索引
            activeFilterType = typeString
            activePresetIndex = index
            
            // 更新设置
            switch type {
            case .polaroid:
                filterService.updateSetting(\.selectedPolaroidPreset, value: index)
            case .film:
                filterService.updateSetting(\.selectedFilmPreset, value: index)
            case .vintage:
                filterService.updateSetting(\.selectedVintagePreset, value: index)
            case .fashion:
                filterService.updateSetting(\.selectedFashionPreset, value: index)
            case .ins:
                filterService.updateSetting(\.selectedINSPreset, value: index)
            }
            filterService.updateSetting(\.activeFilterType, value: typeString)
            filterService.updateSetting(\.activePresetIndex, value: index)
        }
    }
    
    /// 选择宝丽来预设
    /// - Parameter index: 预设索引
    func selectPolaroidPreset(_ index: Int) {
        selectPreset(type: .polaroid, index: index)
    }
    
    /// 选择胶片预设
    /// - Parameter index: 预设索引
    func selectFilmPreset(_ index: Int) {
        selectPreset(type: .film, index: index)
    }
    
    /// 选择复古预设
    /// - Parameter index: 预设索引
    func selectVintagePreset(_ index: Int) {
        selectPreset(type: .vintage, index: index)
    }
    
    /// 选择时尚预设
    /// - Parameter index: 预设索引
    func selectFashionPreset(_ index: Int) {
        selectPreset(type: .fashion, index: index)
    }
    
    /// 选择INS风预设
    /// - Parameter index: 预设索引
    func selectINSPreset(_ index: Int) {
        selectPreset(type: .ins, index: index)
    }
    
    /// 更新随机种子值
    func updateRandomSeed() {
        randomSeed = Int.random(in: 0...1000)
    }
    
    /// 加载保存的滤镜设置
    func loadSavedFilterSettings() {
        let filterSettings = FilterService.shared.getSettings()
        
        // 加载各类预设索引
        selectedPolaroidPreset = filterSettings.selectedPolaroidPreset
        selectedFilmPreset = filterSettings.selectedFilmPreset
        selectedVintagePreset = filterSettings.selectedVintagePreset
        selectedFashionPreset = filterSettings.selectedFashionPreset
        selectedINSPreset = filterSettings.selectedINSPreset
        
        // 加载全局活跃状态
        activeFilterType = filterSettings.activeFilterType
        activePresetIndex = filterSettings.activePresetIndex
    }
}