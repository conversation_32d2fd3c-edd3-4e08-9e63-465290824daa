import Foundation
import SwiftUI
import Combine
import UIKit

/// 特效视图模型 - MVVM-S架构
class EffectsViewModel: ObservableObject {

    // MARK: - 依赖注入
    private let effectsService: EffectsService

    // MARK: - 发布属性
    @Published var lightLeakParameters = LightLeakParameters.default
    @Published var availableLightLeakPresets: [LightLeakPreset] = []
    @Published var selectedLightLeakPreset: LightLeakPreset?

    @Published var grainParameters = GrainParameters.default
    @Published var availableGrainPresets: [GrainPreset] = []
    @Published var selectedGrainPreset: GrainPreset?

    @Published var scratchParameters = ScratchParameters.default
    @Published var availableScratchPresets: [ScratchPreset] = []
    @Published var selectedScratchPreset: ScratchPreset?

   // MARK: - 效果相关状态
    
    /// 时间戳效果是否启用
    @Published var isTimeEnabled: Bool = false
    
    /// 颗粒效果是否启用
    @Published var isGrainEnabled: Bool = false
    
    /// 划痕效果是否启用
    @Published var isScratchEnabled: Bool = false
    
    /// 漏光效果是否启用
    @Published var isLeakEnabled: Bool = false
    
    /// 选中的时间戳样式
    @Published var selectedTimeStyle: String = ""
    
    /// 颗粒效果强度
    @Published var grainIntensity: Double = 0.5
    
    /// 划痕效果强度
    @Published var scratchIntensity: Double = 0.5
    
    /// 漏光效果强度
    @Published var leakIntensity: Double = 0.5
    
    /// 漏光随机种子
    @Published var leakRandomValue: Int = Int.random(in: 1...100)

    // MARK: - 初始化
    init(effectsService: EffectsService) {
        self.effectsService = effectsService
    }

    // MARK: - 初始化特效系统

    /// 初始化特效系统
    func setupEffects() {
        // 从特效服务获取预设
        updateEffectsFromService()

        // 监听特效更新通知
        NotificationService.shared.addObserver(
            self,
            selector: #selector(handleEffectsDidUpdate),
            name: .effectsDidUpdate,
            object: nil
        )
    }

    /// 从特效服务更新本地状态
    private func updateEffectsFromService() {
        let effectsService = EffectsService.shared

        // 更新漏光效果
        lightLeakParameters = effectsService.getLightLeakParameters()
        availableLightLeakPresets = effectsService.getAvailableLightLeakPresets()
        selectedLightLeakPreset = lightLeakParameters.selectedPreset

        // 更新颗粒效果
        grainParameters = effectsService.getGrainParameters()
        availableGrainPresets = effectsService.getAvailableGrainPresets()
        selectedGrainPreset = grainParameters.selectedPreset

        // 更新划痕效果
        scratchParameters = effectsService.getScratchParameters()
        availableScratchPresets = effectsService.getAvailableScratchPresets()
        selectedScratchPreset = scratchParameters.selectedPreset
    }

    /// 处理特效更新通知
    @objc private func handleEffectsDidUpdate(_ notification: Notification) {
        DispatchQueue.main.async {
            self.updateEffectsFromService()
            self.objectWillChange.send()
        }
    }

    // MARK: - 漏光效果方法

    /// 选择漏光预设
    /// - Parameter preset: 要选择的预设，如果为nil则取消选择
    func selectLightLeakPreset(_ preset: LightLeakPreset?) {
        // 委托给特效服务
        EffectsService.shared.selectLightLeakPreset(preset)
    }

    /// 更新漏光强度
    /// - Parameter intensity: 新的强度值 (0.0-1.0)
    func updateLightLeakIntensity(_ intensity: Double) {
        // 委托给特效服务
        EffectsService.shared.updateLightLeakIntensity(intensity)
    }

    /// 更新漏光混合模式
    /// - Parameter blendMode: 新的混合模式
    func updateLightLeakBlendMode(_ blendMode: LightLeakBlendMode) {
        // 委托给特效服务
        EffectsService.shared.updateLightLeakBlendMode(blendMode)
    }

    /// 更新漏光旋转角度
    /// - Parameter rotation: 旋转角度（弧度）
    func updateLightLeakRotation(_ rotation: Double) {
        // 委托给特效服务
        EffectsService.shared.updateLightLeakRotation(rotation)
    }

    /// 更新漏光位置偏移
    /// - Parameter offset: 位置偏移
    func updateLightLeakPosition(_ offset: CGPoint) {
        // 委托给特效服务
        EffectsService.shared.updateLightLeakPosition(offset)
    }

    /// 切换漏光效果开启/关闭状态
    func toggleLightLeakEnabled() {
        // 委托给特效服务
        EffectsService.shared.toggleLightLeakEnabled()
    }

    /// 重置漏光效果参数
    func resetLightLeakParameters() {
        // 委托给特效服务
        EffectsService.shared.resetLightLeakParameters()
    }

    // MARK: - 颗粒效果方法

    /// 选择颗粒预设
    /// - Parameter preset: 要选择的预设，如果为nil则取消选择
    func selectGrainPreset(_ preset: GrainPreset?) {
        // 委托给特效服务
        EffectsService.shared.selectGrainPreset(preset)
    }

    /// 更新颗粒强度
    /// - Parameter intensity: 新的强度值 (0.0-1.0)
    func updateGrainIntensity(_ intensity: Double) {
        // 委托给特效服务
        EffectsService.shared.updateGrainIntensity(intensity)
    }

    /// 切换颗粒效果开启/关闭状态
    func toggleGrainEnabled() {
        // 委托给特效服务
        EffectsService.shared.toggleGrainEnabled()
    }

    /// 重置颗粒效果参数
    func resetGrainParameters() {
        // 委托给特效服务
        EffectsService.shared.resetGrainParameters()
    }

    // MARK: - 划痕效果方法

    /// 选择划痕预设
    /// - Parameter preset: 要选择的预设，如果为nil则取消选择
    func selectScratchPreset(_ preset: ScratchPreset?) {
        // 委托给特效服务
        EffectsService.shared.selectScratchPreset(preset)
    }

    /// 更新划痕强度
    /// - Parameter intensity: 新的强度值 (0.0-1.0)
    func updateScratchIntensity(_ intensity: Double) {
        // 委托给特效服务
        EffectsService.shared.updateScratchIntensity(intensity)
    }

    /// 切换划痕效果开启/关闭状态
    func toggleScratchEnabled() {
        // 委托给特效服务
        EffectsService.shared.toggleScratchEnabled()
    }

    /// 重置划痕效果参数
    func resetScratchParameters() {
        // 委托给特效服务
        EffectsService.shared.resetScratchParameters()
    }

    /// 应用划痕效果到拍摄的照片
    /// - Parameter image: 原始图像
    /// - Returns: 应用了划痕效果的图像
    func applyScratchToImage(_ image: UIImage) -> UIImage {
        // 委托给特效服务
        return EffectsService.shared.applyScratchToImage(image)
    }

    // MARK: - 通用特效方法

    /// 应用当前所有效果到预览
    private func applyCurrentEffects() {
        // 委托给特效服务，此方法自动触发通知
        EffectsService.shared.notifyEffectsUpdated()
    }

    /// 应用漏光效果到拍摄的照片
    /// - Parameter image: 原始图像
    /// - Returns: 应用了漏光效果的图像
    func applyLightLeakToImage(_ image: UIImage) -> UIImage {
        // 委托给特效服务
        return EffectsService.shared.applyLightLeakToImage(image)
    }

    /// 应用颗粒效果到拍摄的照片
    /// - Parameter image: 原始图像
    /// - Returns: 应用了颗粒效果的图像
    func applyGrainToImage(_ image: UIImage) -> UIImage {
        // 委托给特效服务
        return EffectsService.shared.applyGrainToImage(image)
    }

    /// 应用所有特效到拍摄的照片
    /// - Parameter image: 原始图像
    /// - Returns: 应用了所有特效的图像
    func applyAllEffectsToImage(_ image: UIImage) -> UIImage {
        // 委托给特效服务
        return EffectsService.shared.applyAllEffectsToImage(image)
    }

    /// 重置所有特效参数
    func resetAllEffects() {
        // 委托给特效服务
        EffectsService.shared.resetAllEffects()
    }
}
