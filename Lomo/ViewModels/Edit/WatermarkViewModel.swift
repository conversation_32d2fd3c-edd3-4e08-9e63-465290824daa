import Foundation
import SwiftUI
import Combine
import UIKit

// MARK: - 水印ViewModel
/// 水印视图模型，负责管理水印相关的状态
/// **重要：这是一个渐进式重构的初始版本**
/// **当前阶段：仅定义基础属性，不改变WatermarkControlView的任何逻辑**
class WatermarkViewModel: ObservableObject {

    // MARK: - 依赖注入
    private let watermarkService: WatermarkServiceProtocol

    // MARK: - 基础状态属性（从WatermarkControlView迁移的核心状态）

    /// 选中的水印类型
    /// 对应：WatermarkControlView中的 @State private var selectedWatermarkType: String = "none"
    @Published var selectedWatermarkType: String = "none"

    /// 选中的水印分类
    /// 对应：WatermarkControlView中的 @State private var selectedWatermarkCategory: String = "经典"
    @Published var selectedWatermarkCategory: String = "经典"

    /// 过滤后的水印索引列表
    /// 对应：WatermarkControlView中的 @State private var filteredWatermarkIndices: [Int] = Array(0...18)
    @Published var filteredWatermarkIndices: [Int] = Array(0...25)

    /// 当前水印设置
    /// 这将替代直接调用WatermarkSettingsManager.shared.getSettings()
    @Published var currentSettings: WatermarkSettings = WatermarkSettings()

    // MARK: - UI状态
    /// 加载状态
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String = ""

    /// 是否显示错误
    @Published var showError: Bool = false

    // MARK: - 初始化
    /// 初始化ViewModel
    /// - Parameter watermarkService: 水印服务（依赖注入）
    init(watermarkService: WatermarkServiceProtocol) {
        self.watermarkService = watermarkService
        print("🏭 [WatermarkViewModel] 初始化ViewModel（渐进式重构 - 第3步）")

        // 加载初始数据
        loadInitialData()
    }

    // MARK: - 数据加载
    /// 加载初始数据
    private func loadInitialData() {
        print("📖 [WatermarkViewModel] 加载初始数据")

        // 从Service获取当前设置
        currentSettings = watermarkService.getSettings()

        // 设置初始的水印类型
        selectedWatermarkType = currentSettings.activeWatermarkStyleType

        print("✅ [WatermarkViewModel] 初始数据加载完成，当前水印类型: \(selectedWatermarkType)")
    }

    // MARK: - 基础方法（暂时为空实现，为未来迁移准备）

    /// 刷新当前设置
    /// **注意：当前版本仅从Service重新获取设置，不改变现有逻辑**
    func refreshCurrentSettings() {
        print("🔄 [WatermarkViewModel] 刷新当前设置")
        currentSettings = watermarkService.getSettings()
    }

    /// 选择水印分类
    /// **注意：当前版本仅更新状态，实际逻辑仍在WatermarkControlView中**
    /// - Parameter category: 分类名称
    func selectCategory(_ category: String) {
        print("📂 [WatermarkViewModel] 选择分类: \(category)")
        selectedWatermarkCategory = category
        // TODO: 未来版本将在这里实现过滤逻辑
    }

    /// 选择水印
    /// **注意：当前版本仅更新状态，实际逻辑仍在WatermarkControlView中**
    /// - Parameter type: 水印类型
    func selectWatermark(_ type: String) {
        print("🎨 [WatermarkViewModel] 选择水印: \(type)")
        selectedWatermarkType = type
        // TODO: 未来版本将在这里实现应用逻辑
    }

    /// 选择水印（通过索引）
    /// **注意：当前版本仅更新状态，实际逻辑仍在WatermarkControlView中**
    /// - Parameters:
    ///   - index: 水印索引
    ///   - container: 容器视图
    func selectWatermark(at index: Int, container: UIView) {
        print("🎨 [WatermarkViewModel] 选择水印索引: \(index)")

        guard index < filteredWatermarkIndices.count else {
            print("❌ [WatermarkViewModel] 索引超出范围")
            return
        }

        // 获取实际的水印类型（这里需要实现索引到类型的映射）
        let actualIndex = filteredWatermarkIndices[index]
        let watermarkType = getWatermarkTypeForIndex(actualIndex)

        selectedWatermarkType = watermarkType
        // TODO: 未来版本将在这里实现应用逻辑
    }

    /// 更新过滤的水印列表
    /// **注意：当前版本使用简化逻辑，实际逻辑仍在WatermarkControlView中**
    func updateFilteredWatermarks() {
        print("🔄 [WatermarkViewModel] 更新过滤水印列表")
        // TODO: 实现实际的过滤逻辑
        // 暂时使用默认的索引列表
        filteredWatermarkIndices = Array(0...25)
    }

    /// 获取水印类型对应的索引
    /// **注意：这是一个简化版本，实际逻辑在WatermarkControlView中**
    /// - Parameter index: 水印索引
    /// - Returns: 水印类型字符串
    private func getWatermarkTypeForIndex(_ index: Int) -> String {
        // 简化的映射逻辑，实际应该从WatermarkControlView中迁移
        switch index {
        case 0: return "none"
        case 1: return "custom1"
        case 2: return "custom2"
        case 3: return "custom3"
        case 4: return "custom4"
        case 5: return "custom5"
        default: return "custom\(index)"
        }
    }

    /// 更新水印文字
    /// **注意：当前版本仅调用Service，不改变现有UI逻辑**
    /// - Parameter text: 新的文字内容
    func updateWatermarkText(_ text: String) {
        print("✏️ [WatermarkViewModel] 更新水印文字: \(text)")
        watermarkService.updateSetting(\.watermarkText, value: text)
        refreshCurrentSettings()
    }

    /// 切换水印文字启用状态
    /// **注意：当前版本仅调用Service，不改变现有UI逻辑**
    /// - Parameter enabled: 是否启用
    func toggleWatermarkText(_ enabled: Bool) {
        print("🔘 [WatermarkViewModel] 切换水印文字启用: \(enabled)")
        watermarkService.updateSetting(\.isWatermarkTextEnabled, value: enabled)
        refreshCurrentSettings()
    }
}

// MARK: - 扩展方法（为未来迁移准备）
extension WatermarkViewModel {

    /// 获取可见的选项配置
    /// **注意：当前版本返回空数组，保持现有逻辑不变**
    /// - Parameter watermarkType: 水印类型
    /// - Returns: 选项配置数组
    func getVisibleOptions(for watermarkType: String) -> [WatermarkOptionConfig] {
        // 暂时返回空数组，保持现有逻辑不变
        return []
    }

    /// 应用水印到容器
    /// **注意：当前版本不实现，保持现有的WatermarkManagerProvider逻辑**
    /// - Parameter container: 容器视图
    func applyWatermark(to container: UIView) {
        print("🎨 [WatermarkViewModel] applyWatermark() - 暂时不实现，保持现有逻辑")
        // TODO: 未来版本将在这里实现应用逻辑
    }

    // MARK: - 业务逻辑方法（渐进式迁移）

    /// 应用水印样式
    /// **渐进式迁移：将WatermarkControlView.applyCurrentWatermarkStyle()迁移到这里**
    /// - Parameter watermarkType: 水印类型
    func applyWatermarkStyle(for watermarkType: String) {
        print("🎨 [WatermarkViewModel] applyWatermarkStyle(for: \(watermarkType))")

        guard watermarkType != "none" else {
            print("🎨 [WatermarkViewModel] 水印类型为none，跳过应用")
            return
        }

        // 获取当前水印设置
        let settings = watermarkService.getSettings()

        // 使用工厂创建水印样式
        guard let style = WatermarkStyleFactory.createWatermarkStyle(type: watermarkType, settings: settings) else {
            print("🎨 [WatermarkViewModel] 无法创建水印样式，类型: \(watermarkType)")
            return
        }

        // TODO: 将来直接在ViewModel中应用水印样式
        // 暂时通过WatermarkManagerProvider应用（保持兼容性）
        print("🎨 [WatermarkViewModel] 通过WatermarkManagerProvider应用水印样式")
        WatermarkManagerProvider.shared.watermarkManager?.applyWatermarkStyle(style)
    }

    /// 移除当前水印
    /// **渐进式迁移：将WatermarkControlView.removeWatermark()迁移到这里**
    func removeWatermark() {
        print("🎨 [WatermarkViewModel] removeWatermark()")

        // 更新ViewModel状态
        selectedWatermarkType = "none"

        // TODO: 将来直接在ViewModel中移除水印
        // 暂时通过WatermarkManagerProvider移除（保持兼容性）
        print("🎨 [WatermarkViewModel] 通过WatermarkManagerProvider移除水印")
        WatermarkManagerProvider.shared.watermarkManager?.removeCurrentWatermark()
    }

    /// 更新水印类型
    /// **新增方法：统一管理水印类型变更**
    /// - Parameter newType: 新的水印类型
    func updateWatermarkType(_ newType: String) {
        print("🎨 [WatermarkViewModel] updateWatermarkType(\(newType))")

        // 更新状态
        selectedWatermarkType = newType

        // 持久化到设置
        watermarkService.updateSetting(\.activeWatermarkStyleType, value: newType)

        // 应用新的水印样式
        if newType == "none" {
            removeWatermark()
        } else {
            applyWatermarkStyle(for: newType)
        }
    }

    // MARK: - 步骤1.1：PreviewScrollView水印选择方法

    /// 选择水印（通过索引）- 完整复制PreviewScrollView.onItemTap逻辑
    /// **步骤1.2：为PreviewScrollView的onItemTap提供ViewModel方法**
    /// - Parameters:
    ///   - index: 过滤后的索引
    ///   - filteredIndices: 过滤后的水印索引数组
    /// - Returns: 需要更新的宽边框粗细滑块值（如果需要更新的话）
    func selectWatermarkAtIndex(_ index: Int,
                               filteredIndices: [Int]) -> Double? {
        print("🎨 [WatermarkViewModel] selectWatermarkAtIndex: \(index)")

        // 将过滤后的索引转换为实际的水印索引
        let actualIndex = filteredIndices[index]

        // 检查Manager是否可用
        guard let manager = WatermarkManagerProvider.shared.watermarkManager else {
            print("❌ WatermarkManager 不可用")
            return nil
        }

        var styleTypeToApply: String? = nil

        // 完整复制索引到类型的映射逻辑
        switch actualIndex {
        case 0: // 不启用水印
            print("选择：不启用水印")
            styleTypeToApply = "none"
        case 1: // 四周白色边框
            print("选择：预览项1 - 白色边框水印")
            styleTypeToApply = "border_2percent"
        case 2: // 拍立得风格
            print("选择：预览项2 - 拍立得风格水印")
            styleTypeToApply = "polaroid"
        case 3: // 胶片风格
            print("选择：预览项3 - 胶片风格水印")
            styleTypeToApply = "film"
        case 4: // 自定义水印4
            print("选择：预览项4 - 自定义水印4")
            styleTypeToApply = "custom4"
        case 5: // 自定义水印5
            print("选择：预览项5 - 左侧宽边框水印")
            styleTypeToApply = "custom5"
        case 6: // 自定义水印6
            print("选择：预览项6 - 上下宽边框水印")
            styleTypeToApply = "custom6"
        case 7: // 自定义水印7
            print("选择：预览项7 - 自定义水印7")
            styleTypeToApply = "custom7"
        case 8: // 自定义水印8
            print("选择：预览项8 - 自定义水印8")
            styleTypeToApply = "custom8"
        case 9: // 自定义水印9
            print("选择：预览项9 - 自定义水印9")
            styleTypeToApply = "custom9"
        case 10: // 自定义水印10
            print("选择：预览项10 - 右侧宽边框水印")
            styleTypeToApply = "custom10"
        case 11: // 自定义水印11
            print("选择：预览项11 - 自定义水印11")
            styleTypeToApply = "custom11"
        case 12: // 自定义水印12
            print("选择：预览项12 - 自定义水印12")
            styleTypeToApply = "custom12"
        case 13: // 自定义水印13
            print("选择：预览项13 - 自定义水印13")
            styleTypeToApply = "custom13"
        case 14: // 自定义水印14
            print("选择：预览项14 - 自定义水印14")
            styleTypeToApply = "custom14"
        case 15: // 自定义水印15
            print("选择：预览项15 - 自定义水印15")
            styleTypeToApply = "custom15"
        case 16: // 自定义水印16
            print("选择：预览项16 - 自定义水印16")
            styleTypeToApply = "custom16"
        case 17: // 自定义水印17
            print("选择：预览项17 - 自定义水印17")
            styleTypeToApply = "custom17"
        case 18: // 自定义水印18
            print("选择：预览项18 - 自定义水印18")
            styleTypeToApply = "custom18"
        case 19: // 自定义水印19
            print("选择：预览项19 - 自定义水印19")
            styleTypeToApply = "custom19"
        case 20: // 自定义水印20
            print("选择：预览项20 - 自定义水印20")
            styleTypeToApply = "custom20"
        case 21: // 自定义水印21
            print("选择：预览项21 - 自定义水印21")
            styleTypeToApply = "custom21"
        case 22: // 自定义水印22
            print("选择：预览项22 - 自定义水印22")
            styleTypeToApply = "custom22"
        case 23: // 自定义水印23
            print("选择：预览项23 - 拼图水印")
            styleTypeToApply = "custom23"
        case 24: // 自定义水印24
            print("选择：预览项24 - 拼图水印")
            styleTypeToApply = "custom24"
        case 25: // 自定义水印25
            print("选择：预览项25 - 自定义水印25")
            styleTypeToApply = "custom25"
        default:
            print("选择：预览项 \(actualIndex) - 行为未定义，暂不应用样式。")
            break
        }

        // 更新状态变量以反映当前选择
        var updatedWideBorderThickness: Double? = nil

        if let newType = styleTypeToApply {
            selectedWatermarkType = newType

            // 完整复制业务逻辑处理
            updatedWideBorderThickness = handleWatermarkTypeSelection(newType: newType)
        } else if actualIndex == 0 {
            selectedWatermarkType = "none"
        }

        // 持久化选择的水印类型
        watermarkService.updateSetting(\.activeWatermarkStyleType, value: selectedWatermarkType)

        // 如果切换到需要互斥逻辑的水印类型，则主动应用一次互斥规则
        if shouldApplyMutualExclusionForType(selectedWatermarkType) {
            applyMutualExclusionForSettings()
        }

        // 获取水印设置
        let settings = watermarkService.getSettings()

        // 使用工厂创建并应用样式
        if let style = WatermarkStyleFactory.createWatermarkStyle(type: selectedWatermarkType, settings: settings) {
            print("✅ 应用水印样式: \(selectedWatermarkType)")
            manager.applyWatermarkStyle(style)
        } else {
            print("ℹ️ 移除当前水印")
            manager.removeCurrentWatermark()
        }

        return updatedWideBorderThickness
    }

    // MARK: - 辅助方法（从WatermarkControlView复制）

    /// 处理水印类型选择的业务逻辑
    /// **完整复制WatermarkControlView中的业务逻辑**
    /// - Parameter newType: 新的水印类型
    /// - Returns: 需要更新的宽边框粗细滑块值（如果需要更新的话）
    private func handleWatermarkTypeSelection(newType: String) -> Double? {
        // 检查水印13/14类型时是否需要关闭描述选项
        if newType == "custom13" || newType == "custom14" {
            let settings = watermarkService.getSettings()
            let hasLogo = !settings.selectedLogo.isEmpty
            let hasSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty
            let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
            let hasPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF"

            // 统计已启用的常规元素数量
            var enabledCount = 0
            if hasLogo { enabledCount += 1 }
            if hasSignature { enabledCount += 1 }
            if hasText || hasPreference { enabledCount += 1 } // 文字和偏好算一个元素

            // 如果常规元素少于3个，自动关闭描述选项
            if enabledCount < 3 && settings.isWatermarkDescriptionEnabled {
                watermarkService.updateSetting(\.isWatermarkDescriptionEnabled, value: false)
            }
        }

        // 根据水印类型动态设置宽边框粗细的默认值
        var updatedWideBorderThickness: Double? = nil
        if ["polaroid", "custom4", "custom5", "custom9", "custom10", "custom11", "custom12", "custom13", "custom14", "custom15", "custom16", "custom19", "custom25"].contains(newType) {
            let defaultSliderValue = WideBorderThicknessUtils.getDefaultSliderValue(for: newType)
            updatedWideBorderThickness = defaultSliderValue
            // 同时更新到设置中
            watermarkService.updateSetting(\.wideBorderThicknessMultiplier, value: defaultSliderValue)
        }

        // 处理自定义水印10、水印16和水印25的偏好选项限制
        if newType == "custom10" || newType == "custom16" || newType == "custom25" {
            // 获取当前设置
            let settings = watermarkService.getSettings()
            // 检查偏好选项
            if !settings.selectedPreferences.isEmpty {
                // 检查所有已选择的偏好是否有不支持的选项
                let unsupportedOptions = settings.selectedPreferences.filter { prefOption in
                    return prefOption != "OFF" && prefOption != "参数"
                }

                // 如果有不支持的选项
                if !unsupportedOptions.isEmpty {
                    // 检查是否包含参数选项
                    if settings.selectedPreferences.contains("参数") {
                        // 只保留参数选项
                        watermarkService.updateSetting(\.selectedPreferences, value: ["参数"])
                        watermarkService.updateSetting(\.preferenceOption, value: "参数")
                    } else {
                        // 重置为OFF
                        watermarkService.updateSetting(\.selectedPreferences, value: [])
                        watermarkService.updateSetting(\.preferenceOption, value: "OFF")
                    }
                    // 发送通知刷新偏好UI
                    NotificationCenter.default.post(name: Notification.Name("RefreshWatermarkPreferenceUI"), object: nil)
                }
            }

            // 自定义水印10不再执行互斥逻辑，可以同时显示文字和参数
        }

        return updatedWideBorderThickness
    }

    /// 检查特定水印类型是否需要应用互斥逻辑
    /// **完整复制WatermarkControlView.shouldApplyMutualExclusionForType**
    private func shouldApplyMutualExclusionForType(_ type: String) -> Bool {
        // 显式排除custom10和custom16，允许它们同时显示文字和偏好
        if type == "custom10" || type == "custom16" {
            return false
        }
        return type == "custom4" ||
               type == "film" ||
               type == "custom7" ||
               type == "custom8" ||
               type == "custom9" ||
               type == "custom5" ||
               type == "custom11" ||
               type == "custom12" ||
               type == "custom13" ||
               type == "custom14" ||
               type == "border_2percent" ||
               type == "custom18" ||
               type == "custom19" || // 添加自定义水印19
               type == "custom20" || // 添加自定义水印20
               type == "custom21" || // 添加自定义水印21
               type == "custom22"    // 添加自定义水印22
    }

    /// 主动应用水印互斥逻辑到当前设置
    /// **完整复制WatermarkControlView.applyMutualExclusionForSettings**
    private func applyMutualExclusionForSettings() {
        var settings = watermarkService.getSettings() // 获取可变副本

        let hasLogo = !settings.selectedLogo.isEmpty
        let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
        let hasPreference = settings.preferenceOption != "OFF" || !settings.selectedPreferences.isEmpty
        let hasSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty

        // 对于所有互斥逻辑的水印，确保文字和偏好只能有一个开启
            if hasText && hasPreference {
                // 默认关闭偏好，保留文字（可以根据产品需求调整哪个优先）
            print("WatermarkViewModel: 应用切换互斥 - 文字和偏好都开启，关闭偏好")
                watermarkService.updateSetting(\.preferenceOption, value: "OFF")
                watermarkService.updateSetting(\.selectedPreferences, value: []) // 同时清空新格式的偏好数组
                NotificationCenter.default.post(name: Notification.Name("RefreshWatermarkPreferenceUI"), object: nil)
        }

        // 移除这部分特殊逻辑，因为我们现在通过UI配置控制描述选项的可见性

        // 重新获取最新的settings，因为可能已被修改
        settings = watermarkService.getSettings()
        // 确保在应用新类型之前，设置是符合互斥规则的
        // （这一步主要是为了确保 WatermarkManager接收到的是正确的settings，尽管UI可能已通过通知更新）
    }
}