import Foundation
import SwiftUI
import Combine
import PhotosUI

/// 编辑功能的视图模型，处理编辑相关的所有状态和操作
class EditViewModel: ObservableObject {
    // MARK: - 发布属性
    
    // 选中的水印/编辑类别
    @Published var selectedCategory: WatermarkCategory = .watermark
    
    // 图片选择器相关属性
    @Published var isImagePickerShown: Bool = false
    @Published var selectedImageData: Data?
    
    // 新增：存储所有选中的图片及其索引
    @Published var selectedImages: [UIImage] = []
    
    // 新增：为自定义水印23准备的拼图照片数组和提示信息
    @Published var puzzleImages: [UIImage] = []
    @Published var showPuzzlePhotoAlert: Bool = false
    @Published var puzzleAlertMessage: String = ""
    
    @Published var currentPhotoIndex: Int = 0
    
    // MARK: - 调节参数 (Session State)
    @Published var adjustExposure: Double = 0.0
    @Published var adjustBrightness: Double = 0.0
    @Published var adjustContrast: Double = 0.0
    @Published var adjustHighlights: Double = 0.0
    @Published var adjustShadows: Double = 0.0
    
    @Published var adjustTemperature: Double = 0.0
    @Published var adjustTint: Double = 0.0
    @Published var adjustSaturation: Double = 0.0
    @Published var adjustFade: Double = 0.0
    
    @Published var adjustSharpness: Double = 0.0
    @Published var adjustVignette: Double = 0.0
    @Published var adjustChromaticAberration: Double = 0.0
    @Published var adjustGrainSize: Double = 0.0
    @Published var adjustGrainSaturation: Double = 0.0
    
    @Published var adjustSelectedParameter = "exposure"
    
    @Published var adjustSelectedHSLColorIndex: Int = 0
    @Published var adjustHue: Double = 0.0
    @Published var adjustHslSaturation: Double = 0.0
    @Published var adjustLuminance: Double = 0.0
    
    @Published var adjustSelectedToneOption: String = "全局"
    @Published var adjustToneBrightness: Double = 0.0
    @Published var toneHueOffset: CGSize = .zero
    
    // 移除冗余的adjustCurvePoints，统一使用AdjustService作为曲线状态管理
    @Published var adjustCurveColorIndex: Int = 0 // RGB is index 0
    
    // MARK: - 图像相关属性
    
    /// 图像服务
    private let imageService: iOSImageService
    
    /// 屏幕尺寸服务
    private let screenMetrics: ScreenMetricsService
    
    /// 视图控制器服务
    private let viewControllerService: ViewControllerService

    /// 滤镜服务 (替代FilterStateManager)
    private let filterService = FilterService.shared

    /// 水印服务 (替代WatermarkSettingsManager)
    private let watermarkService = WatermarkService()

    /// 选中的图像
    @Published var selectedImage: UIImage?
    
    // MARK: - 清除选中的图像
    
    /// 清除当前选中的图像，用于返回相机预览模式
    func clearSelectedImageForEditing() {
        selectedImage = nil
        selectedImages.removeAll()
        puzzleImages.removeAll()
        PuzzleImageProvider.shared.clear()
        currentPhotoIndex = 0
        // 重置所有编辑参数为默认值
        resetAdjustmentParameters()
        print("🖼️ EditViewModel: Cleared selected image and all editing parameters.")
    }
    
    // MARK: - 初始化方法
    
    /// 初始化方法
    /// - Parameters:
    ///   - imageService: 图像服务
    ///   - screenMetrics: 屏幕尺寸服务
    ///   - viewControllerService: 视图控制器服务
    init(imageService: iOSImageService = .shared, 
         screenMetrics: ScreenMetricsService = .shared,
         viewControllerService: ViewControllerService = .shared) {
        self.imageService = imageService
        self.screenMetrics = screenMetrics
        self.viewControllerService = viewControllerService
        
        // 设置通知监听器
        setupNotificationObservers()
    }
    
    // 设置通知监听器
    private func setupNotificationObservers() {
        let notificationService = NotificationService.shared
        notificationService.addObserver(
            self,
            selector: #selector(handleEditSelectedPhotosNotification(_:)),
            name: Notification.Name("EditSelectedPhotos"),
            object: nil
        )
    }
    
    /// 处理编辑选中照片的通知
    @objc private func handleEditSelectedPhotosNotification(_ notification: Notification) {
        guard let selectedAssets = notification.object as? [PHAsset] else {
            print("🖼️ EditViewModel: 收到的通知对象不是PHAsset数组")
            return
        }

        print("🖼️ EditViewModel: 收到选中照片通知，共\(selectedAssets.count)张照片")
        
        // 获取当前激活的水印类型
        let watermarkSettings = watermarkService.getSettings()
        let watermarkType = watermarkSettings.activeWatermarkStyleType
        
        // 检查是否是拼图水印类型
        if watermarkType == "custom23" || watermarkType == "custom24" {
            // 确定所需照片数量
            let requiredCount: Int
            if watermarkType == "custom23" {
                requiredCount = 4 // 自定义水印23需要4张照片
            } else {
                // 自定义水印24支持2-5张照片
                // 这里只检查照片是否在2-5张范围内，不再使用固定数量
                if selectedAssets.count >= 2 && selectedAssets.count <= 5 {
                    // 对于水印24，直接使用当前选择的照片数量作为需要的照片数量
                    requiredCount = selectedAssets.count
                } else {
                    // 如果不在支持范围内，直接返回
                    return
                }
            }
            
            // 对于水印23仍然需要严格检查照片数量
            if watermarkType == "custom23" && selectedAssets.count != requiredCount {
                // 如果不是所需数量的照片，直接返回，不进行处理
                // 提示已经在GalleryViewModel中处理
                return
            }
            
            // 处理拼图照片加载
            DispatchQueue.main.async {
                self.puzzleImages.removeAll()
                self.selectedImages.removeAll() // 确保清除旧的单张照片
                self.currentPhotoIndex = 0
                
                let imageManager = PHImageManager.default()
                let requestOptions = PHImageRequestOptions()
                requestOptions.isSynchronous = false
                requestOptions.deliveryMode = .highQualityFormat
                requestOptions.resizeMode = .exact

                let group = DispatchGroup()
                var loadedImages: [UIImage?] = Array(repeating: nil, count: selectedAssets.count)

                for (index, asset) in selectedAssets.enumerated() {
                    group.enter()
                    imageManager.requestImage(
                        for: asset,
                        targetSize: PHImageManagerMaximumSize,
                        contentMode: .aspectFit,
                        options: requestOptions
                    ) { image, info in
                        defer { group.leave() }
                        if let image = image {
                            loadedImages[index] = image
                            print("🖼️ EditViewModel (Puzzle): Successfully loaded image at index \(index).")
                        } else {
                            print("🖼️ EditViewModel (Puzzle): Failed to load image at index \(index).")
                        }
                    }
                }

                group.notify(queue: .main) {
                    self.puzzleImages = loadedImages.compactMap { $0 }
                    if self.puzzleImages.count == requiredCount {
                        self.selectedImage = self.puzzleImages.first // 设置预览图为第一张
                        PuzzleImageProvider.shared.images = self.puzzleImages // 将照片存入共享服务

                        // 将第一张图像设置到FilterService
                        if let firstImage = self.puzzleImages.first {
                            print("📸 [DEBUG] EditViewModel (Puzzle): 开始调用 filterService.setOriginalImage()")
                            self.filterService.setOriginalImage(firstImage)
                            print("📸 [DEBUG] EditViewModel (Puzzle): filterService.setOriginalImage() 调用完成")
                        }

                        print("🖼️ EditViewModel (Puzzle): All \(requiredCount) puzzle images loaded successfully.")
                    } else {
                        // 如果加载失败导致照片不足所需数量，清理状态
                        self.puzzleImages.removeAll()
                        self.selectedImage = nil
                        PuzzleImageProvider.shared.clear() // 清理共享服务
                        print("🖼️ EditViewModel (Puzzle): Failed to load all \(requiredCount) images.")
                    }
                }
            }
        } else {
            // 其他所有水印类型的原始逻辑
        DispatchQueue.main.async {
            self.selectedImages.removeAll()
                self.puzzleImages.removeAll() // 确保清除拼图照片
                PuzzleImageProvider.shared.clear() // 确保清除拼图照片服务
            self.currentPhotoIndex = 0
                
            let imageManager = PHImageManager.default()
            let requestOptions = PHImageRequestOptions()
                requestOptions.isSynchronous = false
            requestOptions.deliveryMode = .highQualityFormat
                requestOptions.resizeMode = .exact

            let group = DispatchGroup()
            var loadedImages: [UIImage?] = Array(repeating: nil, count: selectedAssets.count)

            for (index, asset) in selectedAssets.enumerated() {
                group.enter()
                imageManager.requestImage(
                    for: asset,
                        targetSize: PHImageManagerMaximumSize,
                    contentMode: .aspectFit,
                    options: requestOptions
                ) { image, info in
                    defer { group.leave() }
                    if let image = image {
                        loadedImages[index] = image
                        print("🖼️ EditViewModel: Successfully loaded image at index \(index) from notification.")
                    } else {
                        print("🖼️ EditViewModel: Failed to load image at index \(index) from PHAsset received via notification.")
                    }
                }
            }

            group.notify(queue: .main) {
                    self.selectedImages = loadedImages.compactMap { $0 }
                if !self.selectedImages.isEmpty {
                    self.selectedImage = self.selectedImages.first
                    self.currentPhotoIndex = 0

                    // 将第一张图像设置到FilterService
                    if let firstImage = self.selectedImages.first {
                        print("📸 [DEBUG] EditViewModel: 开始调用 filterService.setOriginalImage()")
                        self.filterService.setOriginalImage(firstImage)
                        print("📸 [DEBUG] EditViewModel: filterService.setOriginalImage() 调用完成")
                    }

                    print("🖼️ EditViewModel: All images loaded. Total: \(self.selectedImages.count). Current image set.")
                } else {
                    self.selectedImage = nil
                    print("🖼️ EditViewModel: No images were loaded successfully.")
                    }
                }
            }
        }
    }
    
    // MARK: - Photo Navigation
    
    func previousPhoto() {
        guard !selectedImages.isEmpty else { return }
        if currentPhotoIndex == 0 {
            currentPhotoIndex = selectedImages.count - 1
        } else {
            currentPhotoIndex -= 1
        }
        updateSelectedImage()
    }
    
    func nextPhoto() {
        guard !selectedImages.isEmpty else { return }
        if currentPhotoIndex == selectedImages.count - 1 {
            currentPhotoIndex = 0
        } else {
            currentPhotoIndex += 1
        }
        updateSelectedImage()
    }
    
    private func updateSelectedImage() {
        print("📸 [DEBUG] EditViewModel.updateSelectedImage() 开始 - Index: \(currentPhotoIndex)")

        selectedImage = selectedImages[currentPhotoIndex]

        // 将新的图像设置到FilterService
        if let currentImage = selectedImage {
            print("📸 [DEBUG] EditViewModel: 开始调用 filterService.setOriginalImage() (切换图像)")
            filterService.setOriginalImage(currentImage)
            print("📸 [DEBUG] EditViewModel: filterService.setOriginalImage() 调用完成 (切换图像)")
        }

        print("🖼️ EditViewModel: Navigated to selected image. Index: \(currentPhotoIndex)")
    }
    
    // MARK: - 构图相关方法
    // 注意：构图相关方法已迁移到CropViewModel中，使用MVVM-S架构
    
    // MARK: - 特效相关方法
    
    /// 切换时间戳效果开启/关闭
    func toggleTimeEffect() {
        let settings = EffectsService.shared.getSettings()
        EffectsService.shared.updateSetting(\.isTimeEnabled, value: !settings.isTimeEnabled)
    }

    /// 更新选中的时间戳样式
    /// - Parameter style: 时间戳样式
    func updateTimeStyle(_ style: String) {
        EffectsService.shared.updateSetting(\.selectedTimeStyle, value: style)
    }

    /// 切换颗粒效果开启/关闭
    func toggleGrainEffect() {
        let settings = EffectsService.shared.getSettings()
        EffectsService.shared.updateSetting(\.isGrainEnabled, value: !settings.isGrainEnabled)
    }

    /// 更新颗粒效果强度
    /// - Parameter intensity: 强度值 (0-1)
    func updateGrainIntensity(_ intensity: Double) {
        EffectsService.shared.updateSetting(\.grainIntensity, value: intensity)
    }

    /// 切换划痕效果开启/关闭
    func toggleScratchEffect() {
        let settings = EffectsService.shared.getSettings()
        EffectsService.shared.updateSetting(\.isScratchEnabled, value: !settings.isScratchEnabled)
    }

    /// 更新划痕效果强度
    /// - Parameter intensity: 强度值 (0-1)
    func updateScratchIntensity(_ intensity: Double) {
        EffectsService.shared.updateSetting(\.scratchIntensity, value: intensity)
    }

    /// 切换漏光效果开启/关闭
    func toggleLeakEffect() {
        let settings = EffectsService.shared.getSettings()
        EffectsService.shared.updateSetting(\.isLeakEnabled, value: !settings.isLeakEnabled)
    }

    /// 更新漏光效果强度
    /// - Parameter intensity: 强度值 (0-1)
    func updateLeakIntensity(_ intensity: Double) {
        EffectsService.shared.updateSetting(\.leakIntensity, value: intensity)
    }

    /// 随机生成新的漏光效果
    func randomizeLeakEffect() {
        EffectsService.shared.randomizeLeakEffect()
    }
    
    // MARK: - 调整参数相关方法
    // 注意：相纸预设相关方法已迁移到PaperViewModel中，使用MVVM-S架构
    
    /// 更新选中的参数类别
    /// - Parameter parameter: 参数名称
    func updateSelectedParameter(_ parameter: String) {
        AdjustService.shared.updateSetting(\.selectedParameter, value: parameter)
    }
    
    /// 更新曝光值
    /// - Parameter value: 新的曝光值
    func updateExposure(_ value: Double) {
        AdjustService.shared.updateSetting(\.exposure, value: value)
    }
    
    /// 更新亮度
    /// - Parameter value: 新的亮度值
    func updateBrightness(_ value: Double) {
        AdjustService.shared.updateSetting(\.brightness, value: value)
    }
    
    /// 更新对比度
    /// - Parameter value: 新的对比度值
    func updateContrast(_ value: Double) {
        AdjustService.shared.updateSetting(\.contrast, value: value)
    }
    
    /// 更新高光
    /// - Parameter value: 新的高光值
    func updateHighlights(_ value: Double) {
        AdjustService.shared.updateSetting(\.highlights, value: value)
    }
    
    /// 更新阴影
    /// - Parameter value: 新的阴影值
    func updateShadows(_ value: Double) {
        AdjustService.shared.updateSetting(\.shadows, value: value)
    }
    
    /// 更新色温
    /// - Parameter value: 新的色温值
    func updateTemperature(_ value: Double) {
        AdjustService.shared.updateSetting(\.temperature, value: value)
    }
    
    /// 更新色调
    /// - Parameter value: 新的色调值
    func updateTint(_ value: Double) {
        AdjustService.shared.updateSetting(\.tint, value: value)
    }
    
    /// 更新饱和度
    /// - Parameter value: 新的饱和度值
    func updateSaturation(_ value: Double) {
        AdjustService.shared.updateSetting(\.saturation, value: value)
    }
    
    /// 更新褪色
    /// - Parameter value: 新的褪色值
    func updateFade(_ value: Double) {
        AdjustService.shared.updateSetting(\.fade, value: value)
    }
    
    /// 更新锐度
    /// - Parameter value: 新的锐度值
    func updateSharpness(_ value: Double) {
        AdjustService.shared.updateSetting(\.sharpness, value: value)
    }
    
    /// 更新暗角
    /// - Parameter value: 新的暗角值
    func updateVignette(_ value: Double) {
        AdjustService.shared.updateSetting(\.vignette, value: value)
    }
    
    /// 更新色散
    /// - Parameter value: 新的色散值
    func updateChromaticAberration(_ value: Double) {
        AdjustService.shared.updateSetting(\.chromaticAberration, value: value)
    }
    
    /// 更新颗粒大小
    /// - Parameter value: 新的颗粒大小值
    func updateGrainSize(_ value: Double) {
        AdjustService.shared.updateSetting(\.grainSize, value: value)
    }
    
    /// 更新颗粒饱和度
    /// - Parameter value: 新的颗粒饱和度值
    func updateGrainSaturation(_ value: Double) {
        AdjustService.shared.updateSetting(\.grainSaturation, value: value)
    }
    
    // MARK: - HSL相关方法
    
    /// 更新选中的HSL颜色索引
    /// - Parameter index: 颜色索引
    func updateSelectedHSLColorIndex(_ index: Int) {
        AdjustService.shared.updateSetting(\.selectedHSLColorIndex, value: index)
    }
    
    /// 更新色相
    /// - Parameter value: 新的色相值
    func updateHue(_ value: Double) {
        AdjustService.shared.updateSetting(\.hue, value: value)
    }
    
    /// 更新HSL饱和度
    /// - Parameter value: 新的HSL饱和度值
    func updateHSLSaturation(_ value: Double) {
        AdjustService.shared.updateSetting(\.hslSaturation, value: value)
    }
    
    /// 更新明度
    /// - Parameter value: 新的明度值
    func updateLuminance(_ value: Double) {
        AdjustService.shared.updateSetting(\.luminance, value: value)
    }
    
    // MARK: - 色调相关方法
    
    /// 更新选中的色调选项
    /// - Parameter option: 色调选项
    func updateSelectedToneOption(_ option: String) {
        AdjustService.shared.updateSetting(\.selectedToneOption, value: option)
    }
    
    /// 更新色调亮度
    /// - Parameter value: 新的亮度值
    func updateToneBrightness(_ value: Double) {
        AdjustService.shared.updateSetting(\.toneBrightness, value: value)
    }
    
    /// 更新色调色相
    /// - Parameter value: 新的色相值（0-1范围）
    func updateToneHue(_ value: Double) {
        AdjustService.shared.updateSetting(\.toneHue, value: value)
    }
    
    // MARK: - 曲线相关方法
    
    /// 更新曲线颜色索引
    /// - Parameter index: 颜色索引
    func updateCurveColorIndex(_ index: Int) {
        AdjustService.shared.updateSetting(\.curveColorIndex, value: index)
    }
    
    // MARK: - 水印相关方法
    
    /// 更新水印文本
    /// - Parameter text: 新的文本
    func updateWatermarkText(_ text: String) {
        watermarkService.updateSetting(\.watermarkText, value: text)
    }
    
    /// 切换水印开启/关闭
    func toggleWatermark() {
        let settings = watermarkService.getSettings()
        watermarkService.updateSetting(\.isWatermarkTextEnabled, value: !settings.isWatermarkTextEnabled)
    }

    /// 更新水印偏好设置
    /// - Parameter preference: 偏好选项
    func updateWatermarkPreference(_ preference: PreferenceOption) {
        watermarkService.updateSetting(\.preferenceOption, value: preference.rawValue)
    }
    
    /// 更新水印位置
    /// - Parameter position: 位置选项
    func updateWatermarkPosition(_ position: PositionOption) {
        watermarkService.updateSetting(\.positionOption, value: position.rawValue)
    }
    
    // MARK: - 图片选择相关方法
    
    /// 显示图片选择器
    func showImagePicker() {
        // 切换到相册标签并进入选择模式
        if let rootVC = viewControllerService.getCurrentViewController() as? UIHostingController<SharedTabView> {
            let sharedTabViewModel = rootVC.rootView.viewModel
            
            // 设置标志位，让 GalleryView 在出现时进入选择模式
            sharedTabViewModel.shouldEnterGallerySelectionMode = true
            // 切换到相册标签
            sharedTabViewModel.selectedTab = .gallery
        }
    }
    
    /// 处理选择的图片
    /// - Parameter image: 用户选择的图片
    func processSelectedImage(_ image: UIImage) {
        selectedImage = image
        selectedImageData = image.jpegData(compressionQuality: 0.8)
        
        // 重置编辑参数
        resetAdjustmentParameters()
    }
    
    /// 重置所有调整参数到初始值
    private func resetAdjustmentParameters() {
        // 曝光调整
        adjustExposure = 0.0
        adjustBrightness = 0.0
        adjustContrast = 0.0
        adjustHighlights = 0.0
        adjustShadows = 0.0
        
        // 颜色调整
        adjustTemperature = 0.0
        adjustTint = 0.0
        adjustSaturation = 0.0
        adjustFade = 0.0
        
        // 详细调整
        adjustSharpness = 0.0
        adjustVignette = 0.0
        adjustChromaticAberration = 0.0
        adjustGrainSize = 0.0
        adjustGrainSaturation = 0.0
        
        // HSL调整
        adjustSelectedHSLColorIndex = 0
        adjustHue = 0.0
        adjustHslSaturation = 0.0
        adjustLuminance = 0.0
        
        // 色调分离
        adjustSelectedToneOption = "全局"
        adjustToneBrightness = 0.0
        toneHueOffset = .zero
        
        // 曲线调整 - 使用AdjustService统一管理
        adjustCurveColorIndex = 0 // RGB is index 0
    }
    
    // MARK: - 调整相关方法
    
    /// 重置所有调节参数到默认值
    func resetAdjustments() {
        // Reset ViewModel's internal state
        adjustExposure = 0.0
        adjustBrightness = 0.0
        adjustContrast = 0.0
        adjustHighlights = 0.0
        adjustShadows = 0.0
        adjustTemperature = 0.0
        adjustTint = 0.0
        adjustSaturation = 0.0
        adjustFade = 0.0
        adjustSharpness = 0.0
        adjustVignette = 0.0
        adjustChromaticAberration = 0.0
        adjustGrainSize = 0.0
        adjustGrainSaturation = 0.0
        adjustHue = 0.0
        adjustHslSaturation = 0.0
        adjustLuminance = 0.0
        toneHueOffset = .zero
        adjustToneBrightness = 0.0
        // 重置曲线 - 使用AdjustService统一管理
        AdjustService.shared.resetAllCurves()
        
        // Reset persisted settings via Manager
        AdjustService.shared.resetAllParameters()
        
        // Optionally reset selected parameter UI
        // adjustSelectedParameter = "exposure" 
        // AdjustService.shared.updateSetting(\.selectedParameter, value: "曝光")
    }
} 